import contextlib
import logging
import os
from typing import Any

import httpx
from openai import AsyncOpenAI, OpenAI

from core.base import (
    EmbeddingConfig,
    EmbeddingProvider,
    R2RException,
)

from .utils import truncate_texts_to_token_limit

logger = logging.getLogger()


class NewAPIEmbeddingProvider(EmbeddingProvider):
    """
    专门为new-api转发服务设计的embedding provider
    支持通过new-api转发各种模型，包括aliyun/text-embedding-v4
    """
    
    def __init__(
        self,
        config: EmbeddingConfig,
        *args,
        **kwargs,
    ) -> None:
        super().__init__(config)

        provider = config.provider
        if not provider:
            raise ValueError(
                "Must set provider in order to initialize `NewAPIEmbeddingProvider`."
            )
        if provider != "newapi":
            raise ValueError(
                "NewAPIEmbeddingProvider must be initialized with provider `newapi`."
            )

        self.base_model = config.base_model
        self.base_dimension = config.base_dimension

        # 获取API配置
        self.api_base = getattr(config, 'api_base', None) or config.extra_fields.get('api_base')
        self.api_key = (os.getenv("ALIYUN_API_KEY") or
                       os.getenv("OPENAI_API_KEY") or
                       os.getenv("LITELLM_API_KEY"))

        if not self.api_base:
            raise ValueError("api_base must be provided for NewAPIEmbeddingProvider")
        if not self.api_key:
            raise ValueError("API key must be provided via environment variables")

        # 初始化OpenAI客户端，直接连接到new-api
        self.client = OpenAI(
            api_key=self.api_key,
            base_url=self.api_base
        )
        self.async_client = AsyncOpenAI(
            api_key=self.api_key,
            base_url=self.api_base
        )

        logger.info(f"Initialized NewAPIEmbeddingProvider with model: {self.base_model}")
        logger.info(f"API Base: {self.api_base}")



    async def _execute_task(self, task: dict[str, Any]) -> list[list[float]]:
        texts = task["texts"]

        try:
            logger.info(f"NewAPI embedding request - model: {self.base_model}, texts count: {len(texts)}")

            # 直接使用OpenAI客户端调用new-api
            response = await self.async_client.embeddings.create(
                input=texts,
                model=self.base_model,  # 保持原始模型名称 aliyun/text-embedding-v4
            )
            return [data.embedding for data in response.data]
        except Exception as e:
            error_msg = f"Error getting embeddings: {str(e)}"
            logger.error(error_msg)
            raise R2RException(error_msg, 400) from e

    def _execute_task_sync(self, task: dict[str, Any]) -> list[list[float]]:
        texts = task["texts"]
        try:
            logger.info(f"NewAPI embedding request (sync) - model: {self.base_model}, texts count: {len(texts)}")

            # 直接使用OpenAI客户端调用new-api
            response = self.client.embeddings.create(
                input=texts,
                model=self.base_model,  # 保持原始模型名称 aliyun/text-embedding-v4
            )
            return [data.embedding for data in response.data]
        except Exception as e:
            error_msg = f"Error getting embeddings: {str(e)}"
            logger.error(error_msg)
            raise R2RException(error_msg, 400) from e

    async def async_get_embedding(
        self,
        text: str,
        stage: EmbeddingProvider.Step = EmbeddingProvider.Step.BASE,
        **kwargs,
    ) -> list[float]:
        if stage != EmbeddingProvider.Step.BASE:
            raise ValueError(
                "NewAPIEmbeddingProvider only supports search stage."
            )

        task = {
            "texts": [text],
            "stage": stage,
            "kwargs": kwargs,
        }
        return (await self._execute_with_backoff_async(task))[0]

    def get_embedding(
        self,
        text: str,
        stage: EmbeddingProvider.Step = EmbeddingProvider.Step.BASE,
        **kwargs,
    ) -> list[float]:
        if stage != EmbeddingProvider.Step.BASE:
            raise ValueError(
                "Error getting embeddings: NewAPIEmbeddingProvider only supports search stage."
            )

        task = {
            "texts": [text],
            "stage": stage,
            "kwargs": kwargs,
        }
        return self._execute_with_backoff_sync(task)[0]

    async def async_get_embeddings(
        self,
        texts: list[str],
        stage: EmbeddingProvider.Step = EmbeddingProvider.Step.BASE,
        **kwargs,
    ) -> list[list[float]]:
        if stage != EmbeddingProvider.Step.BASE:
            raise ValueError(
                "NewAPIEmbeddingProvider only supports search stage."
            )

        task = {
            "texts": texts,
            "stage": stage,
            "kwargs": kwargs,
        }
        return await self._execute_with_backoff_async(task)

    def get_embeddings(
        self,
        texts: list[str],
        stage: EmbeddingProvider.Step = EmbeddingProvider.Step.BASE,
        **kwargs,
    ) -> list[list[float]]:
        if stage != EmbeddingProvider.Step.BASE:
            raise ValueError(
                "NewAPIEmbeddingProvider only supports search stage."
            )

        task = {
            "texts": texts,
            "stage": stage,
            "kwargs": kwargs,
        }
        return self._execute_with_backoff_sync(task)

    def rerank(self, query: str, results, stage=None, limit: int = 10):
        """NewAPI provider不支持rerank"""
        return results[:limit]

    async def arerank(self, query: str, results, stage=None, limit: int = 10):
        """NewAPI provider不支持rerank"""
        return results[:limit]
