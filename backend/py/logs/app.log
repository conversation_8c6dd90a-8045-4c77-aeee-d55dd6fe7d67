2025-07-30 11:39:58 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 11:39:58 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='openai/volcengine/deepseek-v3', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='litellm' base_model='openai/text-embedding-3-small' base_dimension=512 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 11:39:58 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='openai/volcengine/deepseek-v3', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='litellm' base_model='openai/text-embedding-3-small' base_dimension=512 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 11:39:58 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='openai/volcengine/deepseek-v3', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 11:39:58 - [32mINFO[0m - Initializing R2RCompletionProvider...[0m
2025-07-30 11:39:58 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='openai/volcengine/deepseek-v3', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 11:40:08 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='openai/volcengine/deepseek-v3', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 11:40:12 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='openai/volcengine/deepseek-v3', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 11:40:12 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='openai/volcengine/deepseek-v3', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 11:40:12 - [32mINFO[0m - Initializing BcryptCryptoProvider[0m
2025-07-30 11:40:12 - [32mINFO[0m - Initializing DatabaseProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='openai/volcengine/deepseek-v3', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='postgres' user=None password=None host=None port=None db_name=None project_name=None postgres_configuration_settings=None default_collection_name='Default' default_collection_description='Your default collection.' collection_summary_system_prompt='system' collection_summary_prompt='collection_summary' disable_create_extension=False batch_size=1 graph_search_results_store_path=None graph_enrichment_settings=GraphEnrichmentSettings(force_graph_search_results_enrichment=False, graph_communities_prompt='graph_communities', max_summary_input_length=65536, generation_config=None, leiden_params={}) graph_creation_settings=GraphCreationSettings(graph_extraction_prompt='graph_extraction', graph_entity_description_prompt='graph_entity_description', entity_types=[], relation_types=[], chunk_merge_count=2, max_knowledge_relationships=100, max_description_input_length=65536, generation_config=None, automatic_deduplication=True) graph_search_settings=GraphSearchSettings(limits={}, enabled=True) limits=LimitSettings(global_per_min=60, route_per_min=20, monthly_limit=10000) maintenance=MaintenanceSettings(vacuum_schedule='0 3 * * *', vacuum_analyze=True, vacuum_full=False) route_limits={} user_limits={}.[0m
2025-07-30 11:40:12 - [32mINFO[0m - Connecting to Postgres via TCP/IP[0m
2025-07-30 11:40:12 - [32mINFO[0m - Initializing `PostgresDatabaseProvider`.[0m
2025-07-30 11:40:12 - [32mINFO[0m - Connecting with 230 connections to `asyncpg.create_pool`.[0m
2025-07-30 11:40:13 - [32mINFO[0m - Successfully connected to Postgres database and created connection pool.[0m
2025-07-30 11:40:14 - [32mINFO[0m - Creating table, if it does not exist: "cscsrag"."documents"[0m
2025-07-30 11:40:16 - [32mINFO[0m - Loading prompts from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts[0m
2025-07-30 11:40:16 - [32mINFO[0m - Loading default prompt: chunk_enrichment from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\chunk_enrichment.yaml.[0m
2025-07-30 11:40:16 - [32mINFO[0m - Loading default prompt: collection_summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\collection_summary.yaml.[0m
2025-07-30 11:40:16 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\dynamic_rag_agent.yaml.[0m
2025-07-30 11:40:16 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent_xml_tooling from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\dynamic_rag_agent_xml_tooling.yaml.[0m
2025-07-30 11:40:16 - [32mINFO[0m - Loading default prompt: graph_communities from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_communities.yaml.[0m
2025-07-30 11:40:16 - [32mINFO[0m - Loading default prompt: graph_entity_description from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_entity_description.yaml.[0m
2025-07-30 11:40:17 - [32mINFO[0m - Loading default prompt: graph_extraction from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_extraction.yaml.[0m
2025-07-30 11:40:17 - [32mINFO[0m - Loading default prompt: hyde from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\hyde.yaml.[0m
2025-07-30 11:40:17 - [32mINFO[0m - Loading default prompt: rag from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\rag.yaml.[0m
2025-07-30 11:40:17 - [32mINFO[0m - Loading default prompt: rag_fusion from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\rag_fusion.yaml.[0m
2025-07-30 11:40:17 - [32mINFO[0m - Loading default prompt: static_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\static_rag_agent.yaml.[0m
2025-07-30 11:40:17 - [32mINFO[0m - Loading default prompt: static_research_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\static_research_agent.yaml.[0m
2025-07-30 11:40:17 - [32mINFO[0m - Loading default prompt: summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\summary.yaml.[0m
2025-07-30 11:40:17 - [32mINFO[0m - Loading default prompt: system from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\system.yaml.[0m
2025-07-30 11:40:18 - [32mINFO[0m - Loading default prompt: vision_img from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\vision_img.yaml.[0m
2025-07-30 11:40:18 - [32mINFO[0m - Loading default prompt: vision_pdf from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\vision_pdf.yaml.[0m
2025-07-30 11:40:22 - [32mINFO[0m - Using existing S3 bucket: rag[0m
2025-07-30 11:40:22 - [32mINFO[0m - Initializing OCRProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='openai/volcengine/deepseek-v3', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='mistral' model='mistral-ocr-latest' concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0[0m
2025-07-30 11:40:22 - [33mWARNING[0m - MISTRAL_API_KEY not set in environment, if you plan to use Mistral OCR, please set it.[0m
2025-07-30 11:40:26 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 11:40:26 - [32mINFO[0m - Initialized extra parser zerox for pdf[0m
2025-07-30 11:40:26 - [32mINFO[0m - Initialized extra parser ocr for pdf[0m
2025-07-30 11:40:26 - [32mINFO[0m - R2RIngestionProvider initialized with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='openai/volcengine/deepseek-v3', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' excluded_parsers=[] chunking_strategy=<ChunkingStrategy.RECURSIVE: 'recursive'> chunk_size=1024 chunk_overlap=512 chunk_enrichment_settings=ChunkEnrichmentSettings(enable_chunk_enrichment=False, n_chunks=2, generation_config=None, chunk_enrichment_prompt='chunk_enrichment') extra_parsers={'pdf': ['zerox', 'ocr']} audio_transcription_model=None vlm=None vlm_batch_size=20 vlm_max_tokens_to_sample=1024 max_concurrent_vlm_tasks=20 vlm_ocr_one_page_per_chunk=True skip_document_summary=False document_summary_system_prompt='system' document_summary_task_prompt='summary' chunks_for_document_summary=128 document_summary_model=None parser_overrides={} automatic_extraction=True document_summary_max_length=100000 separator=None[0m
2025-07-30 11:40:28 - [32mINFO[0m - Initializing database maintenance service[0m
2025-07-30 11:40:28 - [32mINFO[0m - Scheduler started[0m
2025-07-30 11:40:28 - [32mINFO[0m - Scheduler started[0m
2025-07-30 11:40:28 - [32mINFO[0m - Adding job vacuum_database with trigger cron and kwargs {'minute': '0', 'hour': '3', 'day': '*', 'month': '*', 'day_of_week': '*', 'kwargs': {'full': False, 'analyze': True}}[0m
2025-07-30 11:40:28 - [32mINFO[0m - Added job "MaintenanceService.vacuum_database" to job store "default"[0m
2025-07-30 11:40:28 - [32mINFO[0m - Initializing ChunksRouter[0m
2025-07-30 11:40:28 - [32mINFO[0m - Initializing CollectionsRouter[0m
2025-07-30 11:40:28 - [32mINFO[0m - Initializing ConversationsRouter[0m
2025-07-30 11:40:28 - [32mINFO[0m - Initializing DocumentsRouter[0m
2025-07-30 11:40:28 - [32mINFO[0m - Initializing GraphRouter[0m
2025-07-30 11:40:29 - [32mINFO[0m - Initializing IndicesRouter[0m
2025-07-30 11:40:29 - [32mINFO[0m - Initializing PromptsRouter[0m
2025-07-30 11:40:29 - [32mINFO[0m - Initializing RetrievalRouter[0m
2025-07-30 11:40:29 - [32mINFO[0m - Initializing SystemRouter[0m
2025-07-30 11:40:29 - [32mINFO[0m - Initializing UsersRouter[0m
2025-07-30 11:40:30 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 11:40:31 - [32mINFO[0m - Started server process [20720][0m
2025-07-30 11:40:31 - [32mINFO[0m - Waiting for application startup.[0m
2025-07-30 11:40:31 - [32mINFO[0m - Application startup complete.[0m
2025-07-30 11:40:31 - [32mINFO[0m - Uvicorn running on http://0.0.0.0:7272 (Press CTRL+C to quit)[0m
2025-07-30 12:33:45 - [32mINFO[0m - 127.0.0.1:51070 - "POST /v3/users/login HTTP/1.1" [32m200[0m
2025-07-30 12:33:45 - [32mINFO[0m - 127.0.0.1:51070 - "OPTIONS /v3/users/me HTTP/1.1" [32m200[0m
2025-07-30 12:33:46 - [32mINFO[0m - 127.0.0.1:51070 - "GET /v3/users/me HTTP/1.1" [32m200[0m
2025-07-30 12:33:46 - [32mINFO[0m - 127.0.0.1:51070 - "OPTIONS /v3/system/settings HTTP/1.1" [32m200[0m
2025-07-30 12:33:47 - [32mINFO[0m - 127.0.0.1:51070 - "GET /v3/system/settings HTTP/1.1" [32m200[0m
2025-07-30 12:33:50 - [32mINFO[0m - 127.0.0.1:51074 - "OPTIONS /v3/system/status HTTP/1.1" [32m200[0m
2025-07-30 12:33:50 - [32mINFO[0m - 127.0.0.1:51070 - "OPTIONS /v3/system/status HTTP/1.1" [32m200[0m
2025-07-30 12:33:51 - [32mINFO[0m - 127.0.0.1:51074 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-07-30 12:33:51 - [32mINFO[0m - 127.0.0.1:51070 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-07-30 12:34:42 - [32mINFO[0m - 127.0.0.1:51311 - "OPTIONS /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-07-30 12:34:43 - [32mINFO[0m - 127.0.0.1:51311 - "GET /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-07-30 12:35:14 - [32mINFO[0m - 127.0.0.1:51372 - "OPTIONS /v3/documents HTTP/1.1" [32m200[0m
2025-07-30 12:35:14 - [33mWARNING[0m - Skipping data after last boundary[0m
2025-07-30 12:35:15 - [32mINFO[0m - 127.0.0.1:51310 - "GET /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-07-30 12:35:16 - [31mERROR[0m - Error storing file in S3: Parameter validation failed:
Non ascii characters found in S3 metadata for key "filename", value: "03.21.QAT���ȶ���Ѳ������淶˵��.pdf".  
S3 metadata can only contain ASCII characters. [0m
2025-07-30 12:35:16 - [31mERROR[0m - 127.0.0.1:51372 - "POST /v3/documents HTTP/1.1" [31m500[0m
2025-07-30 12:38:44 - [32mINFO[0m - Shutting down[0m
2025-07-30 12:38:45 - [32mINFO[0m - Waiting for application shutdown.[0m
2025-07-30 12:38:45 - [32mINFO[0m - Application shutdown complete.[0m
2025-07-30 12:38:45 - [32mINFO[0m - Finished server process [20720][0m
2025-07-30 12:40:28 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 12:40:28 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='openai/volcengine/deepseek-v3', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='litellm' base_model='openai/text-embedding-3-small' base_dimension=512 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 12:40:28 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='openai/volcengine/deepseek-v3', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='litellm' base_model='openai/text-embedding-3-small' base_dimension=512 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 12:40:28 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='openai/volcengine/deepseek-v3', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 12:40:28 - [32mINFO[0m - Initializing R2RCompletionProvider...[0m
2025-07-30 12:40:28 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='openai/volcengine/deepseek-v3', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 12:40:38 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='openai/volcengine/deepseek-v3', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 12:40:43 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='openai/volcengine/deepseek-v3', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 12:40:43 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='openai/volcengine/deepseek-v3', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 12:40:43 - [32mINFO[0m - Initializing BcryptCryptoProvider[0m
2025-07-30 12:40:43 - [32mINFO[0m - Initializing DatabaseProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='openai/volcengine/deepseek-v3', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='postgres' user=None password=None host=None port=None db_name=None project_name=None postgres_configuration_settings=None default_collection_name='Default' default_collection_description='Your default collection.' collection_summary_system_prompt='system' collection_summary_prompt='collection_summary' disable_create_extension=False batch_size=1 graph_search_results_store_path=None graph_enrichment_settings=GraphEnrichmentSettings(force_graph_search_results_enrichment=False, graph_communities_prompt='graph_communities', max_summary_input_length=65536, generation_config=None, leiden_params={}) graph_creation_settings=GraphCreationSettings(graph_extraction_prompt='graph_extraction', graph_entity_description_prompt='graph_entity_description', entity_types=[], relation_types=[], chunk_merge_count=2, max_knowledge_relationships=100, max_description_input_length=65536, generation_config=None, automatic_deduplication=True) graph_search_settings=GraphSearchSettings(limits={}, enabled=True) limits=LimitSettings(global_per_min=60, route_per_min=20, monthly_limit=10000) maintenance=MaintenanceSettings(vacuum_schedule='0 3 * * *', vacuum_analyze=True, vacuum_full=False) route_limits={} user_limits={}.[0m
2025-07-30 12:40:43 - [32mINFO[0m - Connecting to Postgres via TCP/IP[0m
2025-07-30 12:40:43 - [32mINFO[0m - Initializing `PostgresDatabaseProvider`.[0m
2025-07-30 12:40:43 - [32mINFO[0m - Connecting with 230 connections to `asyncpg.create_pool`.[0m
2025-07-30 12:40:45 - [32mINFO[0m - Successfully connected to Postgres database and created connection pool.[0m
2025-07-30 12:40:45 - [32mINFO[0m - Creating table, if it does not exist: "cscsrag"."documents"[0m
2025-07-30 12:40:47 - [32mINFO[0m - Loading saved prompt: chunk_enrichment[0m
2025-07-30 12:40:47 - [32mINFO[0m - Loading saved prompt: collection_summary[0m
2025-07-30 12:40:47 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent[0m
2025-07-30 12:40:47 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent_xml_tooling[0m
2025-07-30 12:40:47 - [32mINFO[0m - Loading saved prompt: graph_communities[0m
2025-07-30 12:40:47 - [32mINFO[0m - Loading saved prompt: graph_entity_description[0m
2025-07-30 12:40:47 - [32mINFO[0m - Loading saved prompt: graph_extraction[0m
2025-07-30 12:40:47 - [32mINFO[0m - Loading saved prompt: hyde[0m
2025-07-30 12:40:47 - [32mINFO[0m - Loading saved prompt: rag[0m
2025-07-30 12:40:47 - [32mINFO[0m - Loading saved prompt: rag_fusion[0m
2025-07-30 12:40:47 - [32mINFO[0m - Loading saved prompt: static_rag_agent[0m
2025-07-30 12:40:47 - [32mINFO[0m - Loading saved prompt: static_research_agent[0m
2025-07-30 12:40:47 - [32mINFO[0m - Loading saved prompt: summary[0m
2025-07-30 12:40:47 - [32mINFO[0m - Loading saved prompt: system[0m
2025-07-30 12:40:47 - [32mINFO[0m - Loading saved prompt: vision_img[0m
2025-07-30 12:40:47 - [32mINFO[0m - Loading saved prompt: vision_pdf[0m
2025-07-30 12:40:47 - [32mINFO[0m - Loading prompts from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts[0m
2025-07-30 12:40:47 - [32mINFO[0m - Loading default prompt: chunk_enrichment from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\chunk_enrichment.yaml.[0m
2025-07-30 12:40:48 - [32mINFO[0m - Loading default prompt: collection_summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\collection_summary.yaml.[0m
2025-07-30 12:40:48 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\dynamic_rag_agent.yaml.[0m
2025-07-30 12:40:48 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent_xml_tooling from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\dynamic_rag_agent_xml_tooling.yaml.[0m
2025-07-30 12:40:48 - [32mINFO[0m - Loading default prompt: graph_communities from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_communities.yaml.[0m
2025-07-30 12:40:48 - [32mINFO[0m - Loading default prompt: graph_entity_description from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_entity_description.yaml.[0m
2025-07-30 12:40:48 - [32mINFO[0m - Loading default prompt: graph_extraction from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_extraction.yaml.[0m
2025-07-30 12:40:49 - [32mINFO[0m - Loading default prompt: hyde from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\hyde.yaml.[0m
2025-07-30 12:40:49 - [32mINFO[0m - Loading default prompt: rag from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\rag.yaml.[0m
2025-07-30 12:40:49 - [32mINFO[0m - Loading default prompt: rag_fusion from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\rag_fusion.yaml.[0m
2025-07-30 12:40:49 - [32mINFO[0m - Loading default prompt: static_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\static_rag_agent.yaml.[0m
2025-07-30 12:40:49 - [32mINFO[0m - Loading default prompt: static_research_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\static_research_agent.yaml.[0m
2025-07-30 12:40:49 - [32mINFO[0m - Loading default prompt: summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\summary.yaml.[0m
2025-07-30 12:40:49 - [32mINFO[0m - Loading default prompt: system from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\system.yaml.[0m
2025-07-30 12:40:50 - [32mINFO[0m - Loading default prompt: vision_img from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\vision_img.yaml.[0m
2025-07-30 12:40:50 - [32mINFO[0m - Loading default prompt: vision_pdf from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\vision_pdf.yaml.[0m
2025-07-30 12:40:54 - [32mINFO[0m - Using existing S3 bucket: rag[0m
2025-07-30 12:40:54 - [32mINFO[0m - Initializing OCRProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='openai/volcengine/deepseek-v3', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='mistral' model='mistral-ocr-latest' concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0[0m
2025-07-30 12:40:54 - [33mWARNING[0m - MISTRAL_API_KEY not set in environment, if you plan to use Mistral OCR, please set it.[0m
2025-07-30 12:40:58 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:40:58 - [32mINFO[0m - Initialized extra parser zerox for pdf[0m
2025-07-30 12:40:58 - [32mINFO[0m - Initialized extra parser ocr for pdf[0m
2025-07-30 12:40:58 - [32mINFO[0m - R2RIngestionProvider initialized with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='openai/volcengine/deepseek-v3', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' excluded_parsers=[] chunking_strategy=<ChunkingStrategy.RECURSIVE: 'recursive'> chunk_size=1024 chunk_overlap=512 chunk_enrichment_settings=ChunkEnrichmentSettings(enable_chunk_enrichment=False, n_chunks=2, generation_config=None, chunk_enrichment_prompt='chunk_enrichment') extra_parsers={'pdf': ['zerox', 'ocr']} audio_transcription_model=None vlm=None vlm_batch_size=20 vlm_max_tokens_to_sample=1024 max_concurrent_vlm_tasks=20 vlm_ocr_one_page_per_chunk=True skip_document_summary=False document_summary_system_prompt='system' document_summary_task_prompt='summary' chunks_for_document_summary=128 document_summary_model=None parser_overrides={} automatic_extraction=True document_summary_max_length=100000 separator=None[0m
2025-07-30 12:40:58 - [32mINFO[0m - Default admin user already exists.[0m
2025-07-30 12:40:58 - [32mINFO[0m - Initializing database maintenance service[0m
2025-07-30 12:40:58 - [32mINFO[0m - Scheduler started[0m
2025-07-30 12:40:58 - [32mINFO[0m - Scheduler started[0m
2025-07-30 12:40:58 - [32mINFO[0m - Adding job vacuum_database with trigger cron and kwargs {'minute': '0', 'hour': '3', 'day': '*', 'month': '*', 'day_of_week': '*', 'kwargs': {'full': False, 'analyze': True}}[0m
2025-07-30 12:40:58 - [32mINFO[0m - Added job "MaintenanceService.vacuum_database" to job store "default"[0m
2025-07-30 12:40:58 - [32mINFO[0m - Initializing ChunksRouter[0m
2025-07-30 12:40:58 - [32mINFO[0m - Initializing CollectionsRouter[0m
2025-07-30 12:40:58 - [32mINFO[0m - Initializing ConversationsRouter[0m
2025-07-30 12:40:58 - [32mINFO[0m - Initializing DocumentsRouter[0m
2025-07-30 12:40:58 - [32mINFO[0m - Initializing GraphRouter[0m
2025-07-30 12:40:59 - [32mINFO[0m - Initializing IndicesRouter[0m
2025-07-30 12:40:59 - [32mINFO[0m - Initializing PromptsRouter[0m
2025-07-30 12:40:59 - [32mINFO[0m - Initializing RetrievalRouter[0m
2025-07-30 12:40:59 - [32mINFO[0m - Initializing SystemRouter[0m
2025-07-30 12:40:59 - [32mINFO[0m - Initializing UsersRouter[0m
2025-07-30 12:41:00 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 12:41:00 - [32mINFO[0m - Started server process [35564][0m
2025-07-30 12:41:00 - [32mINFO[0m - Waiting for application startup.[0m
2025-07-30 12:41:00 - [32mINFO[0m - Application startup complete.[0m
2025-07-30 12:41:00 - [32mINFO[0m - Uvicorn running on http://0.0.0.0:7272 (Press CTRL+C to quit)[0m
2025-07-30 12:44:37 - [32mINFO[0m - 127.0.0.1:52817 - "POST /v3/users/login HTTP/1.1" [32m200[0m
2025-07-30 12:44:37 - [32mINFO[0m - 127.0.0.1:52817 - "OPTIONS /v3/users/me HTTP/1.1" [32m200[0m
2025-07-30 12:44:37 - [32mINFO[0m - 127.0.0.1:52817 - "GET /v3/users/me HTTP/1.1" [32m200[0m
2025-07-30 12:44:37 - [32mINFO[0m - 127.0.0.1:52817 - "OPTIONS /v3/system/settings HTTP/1.1" [32m200[0m
2025-07-30 12:44:38 - [32mINFO[0m - 127.0.0.1:52817 - "GET /v3/system/settings HTTP/1.1" [32m200[0m
2025-07-30 12:44:38 - [32mINFO[0m - 127.0.0.1:52820 - "OPTIONS /v3/system/status HTTP/1.1" [32m200[0m
2025-07-30 12:44:38 - [32mINFO[0m - 127.0.0.1:52820 - "OPTIONS /v3/system/status HTTP/1.1" [32m200[0m
2025-07-30 12:44:39 - [32mINFO[0m - 127.0.0.1:52817 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-07-30 12:44:40 - [32mINFO[0m - 127.0.0.1:52820 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-07-30 12:44:42 - [32mINFO[0m - 127.0.0.1:52820 - "GET /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-07-30 12:45:23 - [32mINFO[0m - 127.0.0.1:52902 - "OPTIONS /v3/documents HTTP/1.1" [32m200[0m
2025-07-30 12:45:23 - [32mINFO[0m - 127.0.0.1:52903 - "OPTIONS /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-07-30 12:45:23 - [33mWARNING[0m - Skipping data after last boundary[0m
2025-07-30 12:45:23 - [32mINFO[0m - 127.0.0.1:52903 - "GET /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:35 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:45:50 - [32mINFO[0m - Retrying request to /embeddings in 0.419507 seconds[0m
2025-07-30 12:45:50 - [32mINFO[0m - Retrying request to /embeddings in 0.990581 seconds[0m
2025-07-30 12:45:51 - [31mERROR[0m - Error getting embeddings: litellm.ServiceUnavailableError: ServiceUnavailableError: OpenAIException - Error code: 503 - {'error': {'message': '��ǰ���� �Ƽ��з����� �¶���ģ�� text-embedding-3-small �޿������� (request id: 20250730124549523139401GZXn0SNT)', 'type': 'new_api_error'}}[0m
2025-07-30 12:45:51 - [33mWARNING[0m - Request failed (attempt 1): Error getting embeddings: litellm.ServiceUnavailableError: ServiceUnavailableError: OpenAIException - Error code: 503 - {'error': {'message': '��ǰ���� �Ƽ��з����� �¶���ģ�� text-embedding-3-small �޿������� (request id: 20250730124549523139401GZXn0SNT)', 'type': 'new_api_error'}}[0m
2025-07-30 12:45:52 - [32mINFO[0m - Retrying request to /embeddings in 0.407387 seconds[0m
2025-07-30 12:45:52 - [32mINFO[0m - Retrying request to /embeddings in 0.878669 seconds[0m
2025-07-30 12:45:53 - [31mERROR[0m - Error getting embeddings: litellm.ServiceUnavailableError: ServiceUnavailableError: OpenAIException - Error code: 503 - {'error': {'message': '��ǰ���� �Ƽ��з����� �¶���ģ�� text-embedding-3-small �޿������� (request id: 20250730124551392259941urKWtfEX)', 'type': 'new_api_error'}}[0m
2025-07-30 12:45:53 - [33mWARNING[0m - Request failed (attempt 2): Error getting embeddings: litellm.ServiceUnavailableError: ServiceUnavailableError: OpenAIException - Error code: 503 - {'error': {'message': '��ǰ���� �Ƽ��з����� �¶���ģ�� text-embedding-3-small �޿������� (request id: 20250730124551392259941urKWtfEX)', 'type': 'new_api_error'}}[0m
2025-07-30 12:45:54 - [32mINFO[0m - Retrying request to /embeddings in 0.439935 seconds[0m
2025-07-30 12:45:55 - [32mINFO[0m - Retrying request to /embeddings in 0.902434 seconds[0m
2025-07-30 12:45:56 - [31mERROR[0m - Error getting embeddings: litellm.ServiceUnavailableError: ServiceUnavailableError: OpenAIException - Error code: 503 - {'error': {'message': '��ǰ���� �Ƽ��з����� �¶���ģ�� text-embedding-3-small �޿������� (request id: 202507301245541011357617dxRpMHF)', 'type': 'new_api_error'}}[0m
2025-07-30 12:45:56 - [33mWARNING[0m - Request failed (attempt 3): Error getting embeddings: litellm.ServiceUnavailableError: ServiceUnavailableError: OpenAIException - Error code: 503 - {'error': {'message': '��ǰ���� �Ƽ��з����� �¶���ģ�� text-embedding-3-small �޿������� (request id: 202507301245541011357617dxRpMHF)', 'type': 'new_api_error'}}[0m
2025-07-30 12:45:56 - [31mERROR[0m - Error running orchestrated ingestion: Error getting embeddings: litellm.ServiceUnavailableError: ServiceUnavailableError: OpenAIException - Error code: 503 - {'error': {'message': '��ǰ���� �Ƽ��з����� �¶���ģ�� text-embedding-3-small �޿������� (request id: 202507301245541011357617dxRpMHF)', 'type': 'new_api_error'}} 

Attempting to run without orchestration.[0m
2025-07-30 12:45:56 - [32mINFO[0m - Running ingestion without orchestration for file 03.21.QAT���ȶ���Ѳ������淶˵��.pdf and document_id 90bbe979-adc6-5bb9-8548-31309b4e8281.[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 12:46:24 - [32mINFO[0m - Retrying request to /embeddings in 0.489068 seconds[0m
2025-07-30 12:46:24 - [32mINFO[0m - Retrying request to /embeddings in 0.964562 seconds[0m
2025-07-30 12:46:25 - [31mERROR[0m - Error getting embeddings: litellm.ServiceUnavailableError: ServiceUnavailableError: OpenAIException - Error code: 503 - {'error': {'message': '��ǰ���� �Ƽ��з����� �¶���ģ�� text-embedding-3-small �޿������� (request id: 20250730124623690973045KWdjAS7W)', 'type': 'new_api_error'}}[0m
2025-07-30 12:46:25 - [33mWARNING[0m - Request failed (attempt 1): Error getting embeddings: litellm.ServiceUnavailableError: ServiceUnavailableError: OpenAIException - Error code: 503 - {'error': {'message': '��ǰ���� �Ƽ��з����� �¶���ģ�� text-embedding-3-small �޿������� (request id: 20250730124623690973045KWdjAS7W)', 'type': 'new_api_error'}}[0m
2025-07-30 12:46:25 - [32mINFO[0m - Retrying request to /embeddings in 0.444113 seconds[0m
2025-07-30 12:46:26 - [32mINFO[0m - Retrying request to /embeddings in 0.997853 seconds[0m
2025-07-30 12:46:27 - [31mERROR[0m - Error getting embeddings: litellm.ServiceUnavailableError: ServiceUnavailableError: OpenAIException - Error code: 503 - {'error': {'message': '��ǰ���� �Ƽ��з����� �¶���ģ�� text-embedding-3-small �޿������� (request id: 20250730124625424713165jf9ceKA5)', 'type': 'new_api_error'}}[0m
2025-07-30 12:46:27 - [33mWARNING[0m - Request failed (attempt 2): Error getting embeddings: litellm.ServiceUnavailableError: ServiceUnavailableError: OpenAIException - Error code: 503 - {'error': {'message': '��ǰ���� �Ƽ��з����� �¶���ģ�� text-embedding-3-small �޿������� (request id: 20250730124625424713165jf9ceKA5)', 'type': 'new_api_error'}}[0m
2025-07-30 12:46:27 - [32mINFO[0m - Retrying request to /embeddings in 0.468872 seconds[0m
2025-07-30 12:46:28 - [32mINFO[0m - Retrying request to /embeddings in 0.810795 seconds[0m
2025-07-30 12:46:29 - [31mERROR[0m - Error getting embeddings: litellm.ServiceUnavailableError: ServiceUnavailableError: OpenAIException - Error code: 503 - {'error': {'message': '��ǰ���� �Ƽ��з����� �¶���ģ�� text-embedding-3-small �޿������� (request id: 20250730124627271586050tvV8VhKY)', 'type': 'new_api_error'}}[0m
2025-07-30 12:46:29 - [33mWARNING[0m - Request failed (attempt 3): Error getting embeddings: litellm.ServiceUnavailableError: ServiceUnavailableError: OpenAIException - Error code: 503 - {'error': {'message': '��ǰ���� �Ƽ��з����� �¶���ģ�� text-embedding-3-small �޿������� (request id: 20250730124627271586050tvV8VhKY)', 'type': 'new_api_error'}}[0m
2025-07-30 12:46:29 - [33mWARNING[0m - 127.0.0.1:52902 - "POST /v3/documents HTTP/1.1" [33m400[0m
2025-07-30 12:49:22 - [32mINFO[0m - Shutting down[0m
2025-07-30 12:49:22 - [32mINFO[0m - Waiting for application shutdown.[0m
2025-07-30 12:49:22 - [32mINFO[0m - Application shutdown complete.[0m
2025-07-30 12:49:22 - [32mINFO[0m - Finished server process [35564][0m
2025-07-30 12:51:19 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 12:51:19 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='litellm' base_model='aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 12:51:19 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='litellm' base_model='aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 12:51:19 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 12:51:19 - [32mINFO[0m - Initializing R2RCompletionProvider...[0m
2025-07-30 12:51:19 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 12:51:25 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 12:51:27 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 12:51:27 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 12:51:27 - [32mINFO[0m - Initializing BcryptCryptoProvider[0m
2025-07-30 12:51:27 - [32mINFO[0m - Initializing DatabaseProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='postgres' user=None password=None host=None port=None db_name=None project_name=None postgres_configuration_settings=None default_collection_name='Default' default_collection_description='Your default collection.' collection_summary_system_prompt='system' collection_summary_prompt='collection_summary' disable_create_extension=False batch_size=1 graph_search_results_store_path=None graph_enrichment_settings=GraphEnrichmentSettings(force_graph_search_results_enrichment=False, graph_communities_prompt='graph_communities', max_summary_input_length=65536, generation_config=None, leiden_params={}) graph_creation_settings=GraphCreationSettings(graph_extraction_prompt='graph_extraction', graph_entity_description_prompt='graph_entity_description', entity_types=[], relation_types=[], chunk_merge_count=2, max_knowledge_relationships=100, max_description_input_length=65536, generation_config=None, automatic_deduplication=True) graph_search_settings=GraphSearchSettings(limits={}, enabled=True) limits=LimitSettings(global_per_min=60, route_per_min=20, monthly_limit=10000) maintenance=MaintenanceSettings(vacuum_schedule='0 3 * * *', vacuum_analyze=True, vacuum_full=False) route_limits={} user_limits={}.[0m
2025-07-30 12:51:27 - [32mINFO[0m - Connecting to Postgres via TCP/IP[0m
2025-07-30 12:51:27 - [32mINFO[0m - Initializing `PostgresDatabaseProvider`.[0m
2025-07-30 12:51:27 - [32mINFO[0m - Connecting with 230 connections to `asyncpg.create_pool`.[0m
2025-07-30 12:51:28 - [32mINFO[0m - Successfully connected to Postgres database and created connection pool.[0m
2025-07-30 12:51:29 - [32mINFO[0m - Creating table, if it does not exist: "cscsrag"."documents"[0m
2025-07-30 12:51:30 - [31mERROR[0m - Error Dimension mismatch: Table 'cscsrag.chunks' was created with dimension 512, but 1024 was provided. You must use the same dimension for existing tables. while creating R2RProviders.[0m
2025-07-30 12:51:30 - [31mERROR[0m - Failed to start R2R server: Dimension mismatch: Table 'cscsrag.chunks' was created with dimension 512, but 1024 was provided. You must use the same dimension for existing tables.[0m
2025-07-30 13:43:54 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 13:43:54 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='litellm' base_model='aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 13:43:54 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='litellm' base_model='aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 13:43:54 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 13:43:54 - [32mINFO[0m - Initializing R2RCompletionProvider...[0m
2025-07-30 13:43:54 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 13:44:03 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 13:44:06 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 13:44:06 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 13:44:06 - [32mINFO[0m - Initializing BcryptCryptoProvider[0m
2025-07-30 13:44:06 - [32mINFO[0m - Initializing DatabaseProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='postgres' user=None password=None host=None port=None db_name=None project_name=None postgres_configuration_settings=None default_collection_name='Default' default_collection_description='Your default collection.' collection_summary_system_prompt='system' collection_summary_prompt='collection_summary' disable_create_extension=False batch_size=1 graph_search_results_store_path=None graph_enrichment_settings=GraphEnrichmentSettings(force_graph_search_results_enrichment=False, graph_communities_prompt='graph_communities', max_summary_input_length=65536, generation_config=None, leiden_params={}) graph_creation_settings=GraphCreationSettings(graph_extraction_prompt='graph_extraction', graph_entity_description_prompt='graph_entity_description', entity_types=[], relation_types=[], chunk_merge_count=2, max_knowledge_relationships=100, max_description_input_length=65536, generation_config=None, automatic_deduplication=True) graph_search_settings=GraphSearchSettings(limits={}, enabled=True) limits=LimitSettings(global_per_min=60, route_per_min=20, monthly_limit=10000) maintenance=MaintenanceSettings(vacuum_schedule='0 3 * * *', vacuum_analyze=True, vacuum_full=False) route_limits={} user_limits={}.[0m
2025-07-30 13:44:06 - [32mINFO[0m - Connecting to Postgres via TCP/IP[0m
2025-07-30 13:44:06 - [32mINFO[0m - Initializing `PostgresDatabaseProvider`.[0m
2025-07-30 13:44:06 - [32mINFO[0m - Connecting with 230 connections to `asyncpg.create_pool`.[0m
2025-07-30 13:44:08 - [32mINFO[0m - Successfully connected to Postgres database and created connection pool.[0m
2025-07-30 13:44:08 - [32mINFO[0m - Creating table, if it does not exist: "cscsrag"."documents"[0m
2025-07-30 13:44:09 - [31mERROR[0m - Error Dimension mismatch: Table 'cscsrag.chunks' was created with dimension 512, but 1024 was provided. You must use the same dimension for existing tables. while creating R2RProviders.[0m
2025-07-30 13:44:09 - [31mERROR[0m - Failed to start R2R server: Dimension mismatch: Table 'cscsrag.chunks' was created with dimension 512, but 1024 was provided. You must use the same dimension for existing tables.[0m
2025-07-30 13:46:32 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 13:46:32 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='litellm' base_model='aliyun/text-embedding-v4' base_dimension=512 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 13:46:32 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='litellm' base_model='aliyun/text-embedding-v4' base_dimension=512 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 13:46:32 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 13:46:32 - [32mINFO[0m - Initializing R2RCompletionProvider...[0m
2025-07-30 13:46:32 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 13:46:45 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 13:46:49 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 13:46:50 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 13:46:50 - [32mINFO[0m - Initializing BcryptCryptoProvider[0m
2025-07-30 13:46:50 - [32mINFO[0m - Initializing DatabaseProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='postgres' user=None password=None host=None port=None db_name=None project_name=None postgres_configuration_settings=None default_collection_name='Default' default_collection_description='Your default collection.' collection_summary_system_prompt='system' collection_summary_prompt='collection_summary' disable_create_extension=False batch_size=1 graph_search_results_store_path=None graph_enrichment_settings=GraphEnrichmentSettings(force_graph_search_results_enrichment=False, graph_communities_prompt='graph_communities', max_summary_input_length=65536, generation_config=None, leiden_params={}) graph_creation_settings=GraphCreationSettings(graph_extraction_prompt='graph_extraction', graph_entity_description_prompt='graph_entity_description', entity_types=[], relation_types=[], chunk_merge_count=2, max_knowledge_relationships=100, max_description_input_length=65536, generation_config=None, automatic_deduplication=True) graph_search_settings=GraphSearchSettings(limits={}, enabled=True) limits=LimitSettings(global_per_min=60, route_per_min=20, monthly_limit=10000) maintenance=MaintenanceSettings(vacuum_schedule='0 3 * * *', vacuum_analyze=True, vacuum_full=False) route_limits={} user_limits={}.[0m
2025-07-30 13:46:50 - [32mINFO[0m - Connecting to Postgres via TCP/IP[0m
2025-07-30 13:46:50 - [32mINFO[0m - Initializing `PostgresDatabaseProvider`.[0m
2025-07-30 13:46:50 - [32mINFO[0m - Connecting with 230 connections to `asyncpg.create_pool`.[0m
2025-07-30 13:46:52 - [32mINFO[0m - Successfully connected to Postgres database and created connection pool.[0m
2025-07-30 13:46:52 - [32mINFO[0m - Creating table, if it does not exist: "cscsrag"."documents"[0m
2025-07-30 13:46:54 - [32mINFO[0m - Loading saved prompt: collection_summary[0m
2025-07-30 13:46:54 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent[0m
2025-07-30 13:46:54 - [32mINFO[0m - Loading saved prompt: graph_communities[0m
2025-07-30 13:46:54 - [32mINFO[0m - Loading saved prompt: graph_extraction[0m
2025-07-30 13:46:54 - [32mINFO[0m - Loading saved prompt: hyde[0m
2025-07-30 13:46:54 - [32mINFO[0m - Loading saved prompt: rag[0m
2025-07-30 13:46:54 - [32mINFO[0m - Loading saved prompt: rag_fusion[0m
2025-07-30 13:46:54 - [32mINFO[0m - Loading saved prompt: static_rag_agent[0m
2025-07-30 13:46:54 - [32mINFO[0m - Loading saved prompt: static_research_agent[0m
2025-07-30 13:46:54 - [32mINFO[0m - Loading saved prompt: summary[0m
2025-07-30 13:46:54 - [32mINFO[0m - Loading saved prompt: system[0m
2025-07-30 13:46:54 - [32mINFO[0m - Loading saved prompt: vision_img[0m
2025-07-30 13:46:54 - [32mINFO[0m - Loading saved prompt: vision_pdf[0m
2025-07-30 13:46:54 - [32mINFO[0m - Loading saved prompt: chunk_enrichment[0m
2025-07-30 13:46:54 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent_xml_tooling[0m
2025-07-30 13:46:54 - [32mINFO[0m - Loading saved prompt: graph_entity_description[0m
2025-07-30 13:46:54 - [32mINFO[0m - Loading prompts from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts[0m
2025-07-30 13:46:54 - [32mINFO[0m - Loading default prompt: chunk_enrichment from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\chunk_enrichment.yaml.[0m
2025-07-30 13:46:54 - [32mINFO[0m - Loading default prompt: collection_summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\collection_summary.yaml.[0m
2025-07-30 13:46:54 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\dynamic_rag_agent.yaml.[0m
2025-07-30 13:46:54 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent_xml_tooling from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\dynamic_rag_agent_xml_tooling.yaml.[0m
2025-07-30 13:46:55 - [32mINFO[0m - Loading default prompt: graph_communities from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_communities.yaml.[0m
2025-07-30 13:46:55 - [32mINFO[0m - Loading default prompt: graph_entity_description from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_entity_description.yaml.[0m
2025-07-30 13:46:55 - [32mINFO[0m - Loading default prompt: graph_extraction from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_extraction.yaml.[0m
2025-07-30 13:46:55 - [32mINFO[0m - Loading default prompt: hyde from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\hyde.yaml.[0m
2025-07-30 13:46:55 - [32mINFO[0m - Loading default prompt: rag from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\rag.yaml.[0m
2025-07-30 13:46:55 - [32mINFO[0m - Loading default prompt: rag_fusion from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\rag_fusion.yaml.[0m
2025-07-30 13:46:56 - [32mINFO[0m - Loading default prompt: static_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\static_rag_agent.yaml.[0m
2025-07-30 13:46:56 - [32mINFO[0m - Loading default prompt: static_research_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\static_research_agent.yaml.[0m
2025-07-30 13:46:56 - [32mINFO[0m - Loading default prompt: summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\summary.yaml.[0m
2025-07-30 13:46:56 - [32mINFO[0m - Loading default prompt: system from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\system.yaml.[0m
2025-07-30 13:46:56 - [32mINFO[0m - Loading default prompt: vision_img from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\vision_img.yaml.[0m
2025-07-30 13:46:56 - [32mINFO[0m - Loading default prompt: vision_pdf from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\vision_pdf.yaml.[0m
2025-07-30 13:47:05 - [32mINFO[0m - Using existing S3 bucket: rag[0m
2025-07-30 13:47:05 - [32mINFO[0m - Initializing OCRProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='mistral' model='mistral-ocr-latest' concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0[0m
2025-07-30 13:47:05 - [33mWARNING[0m - MISTRAL_API_KEY not set in environment, if you plan to use Mistral OCR, please set it.[0m
2025-07-30 13:47:11 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 13:47:11 - [32mINFO[0m - Initialized extra parser zerox for pdf[0m
2025-07-30 13:47:11 - [32mINFO[0m - Initialized extra parser ocr for pdf[0m
2025-07-30 13:47:11 - [32mINFO[0m - R2RIngestionProvider initialized with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' excluded_parsers=[] chunking_strategy=<ChunkingStrategy.RECURSIVE: 'recursive'> chunk_size=1024 chunk_overlap=512 chunk_enrichment_settings=ChunkEnrichmentSettings(enable_chunk_enrichment=False, n_chunks=2, generation_config=None, chunk_enrichment_prompt='chunk_enrichment') extra_parsers={'pdf': ['zerox', 'ocr']} audio_transcription_model=None vlm=None vlm_batch_size=20 vlm_max_tokens_to_sample=1024 max_concurrent_vlm_tasks=20 vlm_ocr_one_page_per_chunk=True skip_document_summary=False document_summary_system_prompt='system' document_summary_task_prompt='summary' chunks_for_document_summary=128 document_summary_model=None parser_overrides={} automatic_extraction=True document_summary_max_length=100000 separator=None[0m
2025-07-30 13:47:11 - [32mINFO[0m - Default admin user already exists.[0m
2025-07-30 13:47:11 - [32mINFO[0m - Initializing database maintenance service[0m
2025-07-30 13:47:11 - [32mINFO[0m - Scheduler started[0m
2025-07-30 13:47:11 - [32mINFO[0m - Scheduler started[0m
2025-07-30 13:47:11 - [32mINFO[0m - Adding job vacuum_database with trigger cron and kwargs {'minute': '0', 'hour': '3', 'day': '*', 'month': '*', 'day_of_week': '*', 'kwargs': {'full': False, 'analyze': True}}[0m
2025-07-30 13:47:11 - [32mINFO[0m - Added job "MaintenanceService.vacuum_database" to job store "default"[0m
2025-07-30 13:47:11 - [32mINFO[0m - Initializing ChunksRouter[0m
2025-07-30 13:47:11 - [32mINFO[0m - Initializing CollectionsRouter[0m
2025-07-30 13:47:12 - [32mINFO[0m - Initializing ConversationsRouter[0m
2025-07-30 13:47:12 - [32mINFO[0m - Initializing DocumentsRouter[0m
2025-07-30 13:47:12 - [32mINFO[0m - Initializing GraphRouter[0m
2025-07-30 13:47:12 - [32mINFO[0m - Initializing IndicesRouter[0m
2025-07-30 13:47:13 - [32mINFO[0m - Initializing PromptsRouter[0m
2025-07-30 13:47:13 - [32mINFO[0m - Initializing RetrievalRouter[0m
2025-07-30 13:47:13 - [32mINFO[0m - Initializing SystemRouter[0m
2025-07-30 13:47:13 - [32mINFO[0m - Initializing UsersRouter[0m
2025-07-30 13:47:15 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 13:47:15 - [32mINFO[0m - Started server process [21964][0m
2025-07-30 13:47:15 - [32mINFO[0m - Waiting for application startup.[0m
2025-07-30 13:47:15 - [32mINFO[0m - Application startup complete.[0m
2025-07-30 13:47:15 - [32mINFO[0m - Uvicorn running on http://0.0.0.0:7272 (Press CTRL+C to quit)[0m
2025-07-30 14:01:00 - [32mINFO[0m - 127.0.0.1:57854 - "POST /v3/users/login HTTP/1.1" [32m200[0m
2025-07-30 14:01:00 - [32mINFO[0m - 127.0.0.1:57854 - "OPTIONS /v3/users/me HTTP/1.1" [32m200[0m
2025-07-30 14:01:01 - [32mINFO[0m - 127.0.0.1:57854 - "GET /v3/users/me HTTP/1.1" [32m200[0m
2025-07-30 14:01:01 - [32mINFO[0m - 127.0.0.1:57854 - "OPTIONS /v3/system/settings HTTP/1.1" [32m200[0m
2025-07-30 14:01:01 - [32mINFO[0m - 127.0.0.1:57854 - "GET /v3/system/settings HTTP/1.1" [32m200[0m
2025-07-30 14:01:02 - [32mINFO[0m - 127.0.0.1:57855 - "OPTIONS /v3/system/status HTTP/1.1" [32m200[0m
2025-07-30 14:01:02 - [32mINFO[0m - 127.0.0.1:57854 - "OPTIONS /v3/system/status HTTP/1.1" [32m200[0m
2025-07-30 14:01:03 - [32mINFO[0m - 127.0.0.1:57855 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-07-30 14:01:03 - [32mINFO[0m - 127.0.0.1:57854 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-07-30 14:01:39 - [32mINFO[0m - 127.0.0.1:57948 - "OPTIONS /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-07-30 14:01:42 - [32mINFO[0m - 127.0.0.1:57948 - "GET /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-07-30 14:02:08 - [32mINFO[0m - 127.0.0.1:58040 - "OPTIONS /v3/documents/90bbe979-adc6-5bb9-8548-31309b4e8281 HTTP/1.1" [32m200[0m
2025-07-30 14:02:10 - [32mINFO[0m - 127.0.0.1:58040 - "DELETE /v3/documents/90bbe979-adc6-5bb9-8548-31309b4e8281 HTTP/1.1" [32m200[0m
2025-07-30 14:02:51 - [32mINFO[0m - 127.0.0.1:58046 - "OPTIONS /v3/documents HTTP/1.1" [32m200[0m
2025-07-30 14:02:51 - [33mWARNING[0m - Skipping data after last boundary[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:05 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:24 - [33mWARNING[0m - Failed to truncate texts: This model isn't mapped yet. model=aliyun/text-embedding-v4, custom_llm_provider=None. Add it here - https://github.com/BerriAI/litellm/blob/main/model_prices_and_context_window.json.[0m
2025-07-30 14:03:24 - [31mERROR[0m - Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 14:03:24 - [33mWARNING[0m - Request failed (attempt 1): Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 14:03:24 - [33mWARNING[0m - Failed to truncate texts: This model isn't mapped yet. model=aliyun/text-embedding-v4, custom_llm_provider=None. Add it here - https://github.com/BerriAI/litellm/blob/main/model_prices_and_context_window.json.[0m
2025-07-30 14:03:24 - [31mERROR[0m - Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 14:03:24 - [33mWARNING[0m - Request failed (attempt 2): Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 14:03:25 - [33mWARNING[0m - Failed to truncate texts: This model isn't mapped yet. model=aliyun/text-embedding-v4, custom_llm_provider=None. Add it here - https://github.com/BerriAI/litellm/blob/main/model_prices_and_context_window.json.[0m
2025-07-30 14:03:25 - [31mERROR[0m - Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 14:03:25 - [33mWARNING[0m - Request failed (attempt 3): Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 14:03:25 - [31mERROR[0m - Error running orchestrated ingestion: Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers 

Attempting to run without orchestration.[0m
2025-07-30 14:03:25 - [32mINFO[0m - Running ingestion without orchestration for file 03.21.QAT���ȶ���Ѳ������淶˵��.pdf and document_id 90bbe979-adc6-5bb9-8548-31309b4e8281.[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:32 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:03:46 - [33mWARNING[0m - Failed to truncate texts: This model isn't mapped yet. model=aliyun/text-embedding-v4, custom_llm_provider=None. Add it here - https://github.com/BerriAI/litellm/blob/main/model_prices_and_context_window.json.[0m
2025-07-30 14:03:46 - [31mERROR[0m - Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 14:03:46 - [33mWARNING[0m - Request failed (attempt 1): Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 14:03:47 - [33mWARNING[0m - Failed to truncate texts: This model isn't mapped yet. model=aliyun/text-embedding-v4, custom_llm_provider=None. Add it here - https://github.com/BerriAI/litellm/blob/main/model_prices_and_context_window.json.[0m
2025-07-30 14:03:47 - [31mERROR[0m - Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 14:03:47 - [33mWARNING[0m - Request failed (attempt 2): Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 14:03:48 - [33mWARNING[0m - Failed to truncate texts: This model isn't mapped yet. model=aliyun/text-embedding-v4, custom_llm_provider=None. Add it here - https://github.com/BerriAI/litellm/blob/main/model_prices_and_context_window.json.[0m
2025-07-30 14:03:48 - [31mERROR[0m - Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 14:03:48 - [33mWARNING[0m - Request failed (attempt 3): Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 14:03:49 - [33mWARNING[0m - 127.0.0.1:58046 - "POST /v3/documents HTTP/1.1" [33m400[0m
2025-07-30 14:06:03 - [32mINFO[0m - 127.0.0.1:58522 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 14:06:03 - [32mINFO[0m - 127.0.0.1:58522 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 14:11:04 - [32mINFO[0m - 127.0.0.1:59384 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 14:13:18 - [32mINFO[0m - Shutting down[0m
2025-07-30 14:13:18 - [32mINFO[0m - Waiting for application shutdown.[0m
2025-07-30 14:13:18 - [32mINFO[0m - Application shutdown complete.[0m
2025-07-30 14:13:18 - [32mINFO[0m - Finished server process [21964][0m
2025-07-30 14:15:22 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 14:15:22 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://***********:3000/v1'} provider='litellm' base_model='aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 14:15:22 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://***********:3000/v1'} provider='litellm' base_model='aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 14:15:22 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 14:15:22 - [32mINFO[0m - Initializing R2RCompletionProvider...[0m
2025-07-30 14:15:22 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 14:15:34 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 14:15:37 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 14:15:37 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 14:15:37 - [32mINFO[0m - Initializing BcryptCryptoProvider[0m
2025-07-30 14:15:37 - [32mINFO[0m - Initializing DatabaseProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='postgres' user=None password=None host=None port=None db_name=None project_name=None postgres_configuration_settings=None default_collection_name='Default' default_collection_description='Your default collection.' collection_summary_system_prompt='system' collection_summary_prompt='collection_summary' disable_create_extension=False batch_size=1 graph_search_results_store_path=None graph_enrichment_settings=GraphEnrichmentSettings(force_graph_search_results_enrichment=False, graph_communities_prompt='graph_communities', max_summary_input_length=65536, generation_config=None, leiden_params={}) graph_creation_settings=GraphCreationSettings(graph_extraction_prompt='graph_extraction', graph_entity_description_prompt='graph_entity_description', entity_types=[], relation_types=[], chunk_merge_count=2, max_knowledge_relationships=100, max_description_input_length=65536, generation_config=None, automatic_deduplication=True) graph_search_settings=GraphSearchSettings(limits={}, enabled=True) limits=LimitSettings(global_per_min=60, route_per_min=20, monthly_limit=10000) maintenance=MaintenanceSettings(vacuum_schedule='0 3 * * *', vacuum_analyze=True, vacuum_full=False) route_limits={} user_limits={}.[0m
2025-07-30 14:15:37 - [32mINFO[0m - Connecting to Postgres via TCP/IP[0m
2025-07-30 14:15:37 - [32mINFO[0m - Initializing `PostgresDatabaseProvider`.[0m
2025-07-30 14:15:37 - [32mINFO[0m - Connecting with 230 connections to `asyncpg.create_pool`.[0m
2025-07-30 14:15:39 - [32mINFO[0m - Successfully connected to Postgres database and created connection pool.[0m
2025-07-30 14:15:40 - [32mINFO[0m - Creating table, if it does not exist: "cscsrag"."documents"[0m
2025-07-30 14:15:41 - [31mERROR[0m - Error Dimension mismatch: Table 'cscsrag.chunks' was created with dimension 512, but 1024 was provided. You must use the same dimension for existing tables. while creating R2RProviders.[0m
2025-07-30 14:15:41 - [31mERROR[0m - Failed to start R2R server: Dimension mismatch: Table 'cscsrag.chunks' was created with dimension 512, but 1024 was provided. You must use the same dimension for existing tables.[0m
2025-07-30 14:17:37 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 14:17:37 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://***********:3000/v1'} provider='litellm' base_model='aliyun/text-embedding-v4' base_dimension=512 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 14:17:37 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://***********:3000/v1'} provider='litellm' base_model='aliyun/text-embedding-v4' base_dimension=512 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 14:17:37 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 14:17:37 - [32mINFO[0m - Initializing R2RCompletionProvider...[0m
2025-07-30 14:17:37 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 14:17:54 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 14:17:58 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 14:17:58 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 14:17:58 - [32mINFO[0m - Initializing BcryptCryptoProvider[0m
2025-07-30 14:17:58 - [32mINFO[0m - Initializing DatabaseProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='postgres' user=None password=None host=None port=None db_name=None project_name=None postgres_configuration_settings=None default_collection_name='Default' default_collection_description='Your default collection.' collection_summary_system_prompt='system' collection_summary_prompt='collection_summary' disable_create_extension=False batch_size=1 graph_search_results_store_path=None graph_enrichment_settings=GraphEnrichmentSettings(force_graph_search_results_enrichment=False, graph_communities_prompt='graph_communities', max_summary_input_length=65536, generation_config=None, leiden_params={}) graph_creation_settings=GraphCreationSettings(graph_extraction_prompt='graph_extraction', graph_entity_description_prompt='graph_entity_description', entity_types=[], relation_types=[], chunk_merge_count=2, max_knowledge_relationships=100, max_description_input_length=65536, generation_config=None, automatic_deduplication=True) graph_search_settings=GraphSearchSettings(limits={}, enabled=True) limits=LimitSettings(global_per_min=60, route_per_min=20, monthly_limit=10000) maintenance=MaintenanceSettings(vacuum_schedule='0 3 * * *', vacuum_analyze=True, vacuum_full=False) route_limits={} user_limits={}.[0m
2025-07-30 14:17:58 - [32mINFO[0m - Connecting to Postgres via TCP/IP[0m
2025-07-30 14:17:58 - [32mINFO[0m - Initializing `PostgresDatabaseProvider`.[0m
2025-07-30 14:17:58 - [32mINFO[0m - Connecting with 230 connections to `asyncpg.create_pool`.[0m
2025-07-30 14:18:01 - [32mINFO[0m - Successfully connected to Postgres database and created connection pool.[0m
2025-07-30 14:18:01 - [32mINFO[0m - Creating table, if it does not exist: "cscsrag"."documents"[0m
2025-07-30 14:18:03 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent[0m
2025-07-30 14:18:03 - [32mINFO[0m - Loading saved prompt: collection_summary[0m
2025-07-30 14:18:03 - [32mINFO[0m - Loading saved prompt: graph_communities[0m
2025-07-30 14:18:03 - [32mINFO[0m - Loading saved prompt: rag_fusion[0m
2025-07-30 14:18:03 - [32mINFO[0m - Loading saved prompt: static_rag_agent[0m
2025-07-30 14:18:03 - [32mINFO[0m - Loading saved prompt: static_research_agent[0m
2025-07-30 14:18:03 - [32mINFO[0m - Loading saved prompt: summary[0m
2025-07-30 14:18:03 - [32mINFO[0m - Loading saved prompt: system[0m
2025-07-30 14:18:03 - [32mINFO[0m - Loading saved prompt: vision_pdf[0m
2025-07-30 14:18:03 - [32mINFO[0m - Loading saved prompt: graph_extraction[0m
2025-07-30 14:18:03 - [32mINFO[0m - Loading saved prompt: hyde[0m
2025-07-30 14:18:03 - [32mINFO[0m - Loading saved prompt: rag[0m
2025-07-30 14:18:03 - [32mINFO[0m - Loading saved prompt: vision_img[0m
2025-07-30 14:18:03 - [32mINFO[0m - Loading saved prompt: chunk_enrichment[0m
2025-07-30 14:18:03 - [32mINFO[0m - Loading saved prompt: graph_entity_description[0m
2025-07-30 14:18:03 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent_xml_tooling[0m
2025-07-30 14:18:03 - [32mINFO[0m - Loading prompts from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts[0m
2025-07-30 14:18:03 - [32mINFO[0m - Loading default prompt: chunk_enrichment from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\chunk_enrichment.yaml.[0m
2025-07-30 14:18:03 - [32mINFO[0m - Loading default prompt: collection_summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\collection_summary.yaml.[0m
2025-07-30 14:18:04 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\dynamic_rag_agent.yaml.[0m
2025-07-30 14:18:04 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent_xml_tooling from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\dynamic_rag_agent_xml_tooling.yaml.[0m
2025-07-30 14:18:04 - [32mINFO[0m - Loading default prompt: graph_communities from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_communities.yaml.[0m
2025-07-30 14:18:04 - [32mINFO[0m - Loading default prompt: graph_entity_description from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_entity_description.yaml.[0m
2025-07-30 14:18:04 - [32mINFO[0m - Loading default prompt: graph_extraction from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_extraction.yaml.[0m
2025-07-30 14:18:04 - [32mINFO[0m - Loading default prompt: hyde from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\hyde.yaml.[0m
2025-07-30 14:18:04 - [32mINFO[0m - Loading default prompt: rag from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\rag.yaml.[0m
2025-07-30 14:18:05 - [32mINFO[0m - Loading default prompt: rag_fusion from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\rag_fusion.yaml.[0m
2025-07-30 14:18:05 - [32mINFO[0m - Loading default prompt: static_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\static_rag_agent.yaml.[0m
2025-07-30 14:18:05 - [32mINFO[0m - Loading default prompt: static_research_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\static_research_agent.yaml.[0m
2025-07-30 14:18:05 - [32mINFO[0m - Loading default prompt: summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\summary.yaml.[0m
2025-07-30 14:18:05 - [32mINFO[0m - Loading default prompt: system from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\system.yaml.[0m
2025-07-30 14:18:05 - [32mINFO[0m - Loading default prompt: vision_img from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\vision_img.yaml.[0m
2025-07-30 14:18:05 - [32mINFO[0m - Loading default prompt: vision_pdf from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\vision_pdf.yaml.[0m
2025-07-30 14:18:12 - [32mINFO[0m - Using existing S3 bucket: rag[0m
2025-07-30 14:18:12 - [32mINFO[0m - Initializing OCRProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='mistral' model='mistral-ocr-latest' concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0[0m
2025-07-30 14:18:12 - [33mWARNING[0m - MISTRAL_API_KEY not set in environment, if you plan to use Mistral OCR, please set it.[0m
2025-07-30 14:18:17 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:18:17 - [32mINFO[0m - Initialized extra parser zerox for pdf[0m
2025-07-30 14:18:17 - [32mINFO[0m - Initialized extra parser ocr for pdf[0m
2025-07-30 14:18:17 - [32mINFO[0m - R2RIngestionProvider initialized with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' excluded_parsers=[] chunking_strategy=<ChunkingStrategy.RECURSIVE: 'recursive'> chunk_size=1024 chunk_overlap=512 chunk_enrichment_settings=ChunkEnrichmentSettings(enable_chunk_enrichment=False, n_chunks=2, generation_config=None, chunk_enrichment_prompt='chunk_enrichment') extra_parsers={'pdf': ['zerox', 'ocr']} audio_transcription_model=None vlm=None vlm_batch_size=20 vlm_max_tokens_to_sample=1024 max_concurrent_vlm_tasks=20 vlm_ocr_one_page_per_chunk=True skip_document_summary=False document_summary_system_prompt='system' document_summary_task_prompt='summary' chunks_for_document_summary=128 document_summary_model=None parser_overrides={} automatic_extraction=True document_summary_max_length=100000 separator=None[0m
2025-07-30 14:18:18 - [32mINFO[0m - Default admin user already exists.[0m
2025-07-30 14:18:18 - [32mINFO[0m - Initializing database maintenance service[0m
2025-07-30 14:18:18 - [32mINFO[0m - Scheduler started[0m
2025-07-30 14:18:18 - [32mINFO[0m - Scheduler started[0m
2025-07-30 14:18:18 - [32mINFO[0m - Adding job vacuum_database with trigger cron and kwargs {'minute': '0', 'hour': '3', 'day': '*', 'month': '*', 'day_of_week': '*', 'kwargs': {'full': False, 'analyze': True}}[0m
2025-07-30 14:18:18 - [32mINFO[0m - Added job "MaintenanceService.vacuum_database" to job store "default"[0m
2025-07-30 14:18:18 - [32mINFO[0m - Initializing ChunksRouter[0m
2025-07-30 14:18:18 - [32mINFO[0m - Initializing CollectionsRouter[0m
2025-07-30 14:18:18 - [32mINFO[0m - Initializing ConversationsRouter[0m
2025-07-30 14:18:18 - [32mINFO[0m - Initializing DocumentsRouter[0m
2025-07-30 14:18:19 - [32mINFO[0m - Initializing GraphRouter[0m
2025-07-30 14:18:19 - [32mINFO[0m - Initializing IndicesRouter[0m
2025-07-30 14:18:20 - [32mINFO[0m - Initializing PromptsRouter[0m
2025-07-30 14:18:20 - [32mINFO[0m - Initializing RetrievalRouter[0m
2025-07-30 14:18:20 - [32mINFO[0m - Initializing SystemRouter[0m
2025-07-30 14:18:20 - [32mINFO[0m - Initializing UsersRouter[0m
2025-07-30 14:18:21 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 14:18:21 - [32mINFO[0m - Started server process [14624][0m
2025-07-30 14:18:21 - [32mINFO[0m - Waiting for application startup.[0m
2025-07-30 14:18:21 - [32mINFO[0m - Application startup complete.[0m
2025-07-30 14:18:21 - [32mINFO[0m - Uvicorn running on http://0.0.0.0:7272 (Press CTRL+C to quit)[0m
2025-07-30 14:19:31 - [32mINFO[0m - 127.0.0.1:60737 - "POST /v3/users/login HTTP/1.1" [32m200[0m
2025-07-30 14:19:31 - [32mINFO[0m - 127.0.0.1:60737 - "OPTIONS /v3/users/me HTTP/1.1" [32m200[0m
2025-07-30 14:19:32 - [32mINFO[0m - 127.0.0.1:60737 - "GET /v3/users/me HTTP/1.1" [32m200[0m
2025-07-30 14:19:32 - [32mINFO[0m - 127.0.0.1:60737 - "OPTIONS /v3/system/settings HTTP/1.1" [32m200[0m
2025-07-30 14:19:33 - [32mINFO[0m - 127.0.0.1:60737 - "GET /v3/system/settings HTTP/1.1" [32m200[0m
2025-07-30 14:19:34 - [32mINFO[0m - 127.0.0.1:60738 - "OPTIONS /v3/system/status HTTP/1.1" [32m200[0m
2025-07-30 14:19:34 - [32mINFO[0m - 127.0.0.1:60737 - "OPTIONS /v3/system/status HTTP/1.1" [32m200[0m
2025-07-30 14:19:35 - [32mINFO[0m - 127.0.0.1:60738 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-07-30 14:19:36 - [32mINFO[0m - 127.0.0.1:60737 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-07-30 14:19:41 - [32mINFO[0m - 127.0.0.1:60737 - "OPTIONS /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-07-30 14:19:41 - [32mINFO[0m - 127.0.0.1:60737 - "GET /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-07-30 14:19:50 - [32mINFO[0m - 127.0.0.1:60795 - "OPTIONS /v3/documents/90bbe979-adc6-5bb9-8548-31309b4e8281 HTTP/1.1" [32m200[0m
2025-07-30 14:19:52 - [32mINFO[0m - 127.0.0.1:60795 - "DELETE /v3/documents/90bbe979-adc6-5bb9-8548-31309b4e8281 HTTP/1.1" [32m200[0m
2025-07-30 14:20:30 - [32mINFO[0m - 127.0.0.1:60797 - "OPTIONS /v3/documents HTTP/1.1" [32m200[0m
2025-07-30 14:20:30 - [33mWARNING[0m - Skipping data after last boundary[0m
2025-07-30 14:20:33 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:20:33 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:20:33 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:20:57 - [33mWARNING[0m - Failed to truncate texts: This model isn't mapped yet. model=aliyun/text-embedding-v4, custom_llm_provider=None. Add it here - https://github.com/BerriAI/litellm/blob/main/model_prices_and_context_window.json.[0m
2025-07-30 14:20:57 - [31mERROR[0m - Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 14:20:57 - [33mWARNING[0m - Request failed (attempt 1): Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 14:20:57 - [33mWARNING[0m - Failed to truncate texts: This model isn't mapped yet. model=aliyun/text-embedding-v4, custom_llm_provider=None. Add it here - https://github.com/BerriAI/litellm/blob/main/model_prices_and_context_window.json.[0m
2025-07-30 14:20:57 - [31mERROR[0m - Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 14:20:57 - [33mWARNING[0m - Request failed (attempt 2): Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 14:20:58 - [33mWARNING[0m - Failed to truncate texts: This model isn't mapped yet. model=aliyun/text-embedding-v4, custom_llm_provider=None. Add it here - https://github.com/BerriAI/litellm/blob/main/model_prices_and_context_window.json.[0m
2025-07-30 14:20:58 - [31mERROR[0m - Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 14:20:58 - [33mWARNING[0m - Request failed (attempt 3): Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 14:20:58 - [31mERROR[0m - Error running orchestrated ingestion: Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers 

Attempting to run without orchestration.[0m
2025-07-30 14:20:58 - [32mINFO[0m - Running ingestion without orchestration for file ��ְ�����ݽ���.pdf and document_id 55b09cdc-5de0-567a-bf36-dcdd237269fa.[0m
2025-07-30 14:20:59 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:20:59 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:20:59 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:21:19 - [33mWARNING[0m - Failed to truncate texts: This model isn't mapped yet. model=aliyun/text-embedding-v4, custom_llm_provider=None. Add it here - https://github.com/BerriAI/litellm/blob/main/model_prices_and_context_window.json.[0m
2025-07-30 14:21:19 - [31mERROR[0m - Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 14:21:20 - [33mWARNING[0m - Request failed (attempt 1): Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 14:21:20 - [33mWARNING[0m - Failed to truncate texts: This model isn't mapped yet. model=aliyun/text-embedding-v4, custom_llm_provider=None. Add it here - https://github.com/BerriAI/litellm/blob/main/model_prices_and_context_window.json.[0m
2025-07-30 14:21:21 - [31mERROR[0m - Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 14:21:21 - [33mWARNING[0m - Request failed (attempt 2): Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 14:21:22 - [33mWARNING[0m - Failed to truncate texts: This model isn't mapped yet. model=aliyun/text-embedding-v4, custom_llm_provider=None. Add it here - https://github.com/BerriAI/litellm/blob/main/model_prices_and_context_window.json.[0m
2025-07-30 14:21:22 - [31mERROR[0m - Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 14:21:22 - [33mWARNING[0m - Request failed (attempt 3): Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 14:21:22 - [33mWARNING[0m - 127.0.0.1:60797 - "POST /v3/documents HTTP/1.1" [33m400[0m
2025-07-30 14:25:03 - [32mINFO[0m - 127.0.0.1:61676 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 14:25:04 - [32mINFO[0m - 127.0.0.1:61676 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 14:30:10 - [32mINFO[0m - 127.0.0.1:62618 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 14:35:11 - [32mINFO[0m - 127.0.0.1:63358 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 14:35:11 - [32mINFO[0m - 127.0.0.1:63358 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 14:40:12 - [32mINFO[0m - 127.0.0.1:64242 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 14:45:13 - [32mINFO[0m - 127.0.0.1:65085 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 14:45:15 - [32mINFO[0m - 127.0.0.1:65085 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 14:45:53 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 14:45:53 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://***********:3000/v1'} provider='litellm' base_model='openai/aliyun/text-embedding-v4' base_dimension=512 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 14:45:53 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://***********:3000/v1'} provider='litellm' base_model='openai/aliyun/text-embedding-v4' base_dimension=512 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 14:45:53 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 14:45:53 - [32mINFO[0m - Initializing R2RCompletionProvider...[0m
2025-07-30 14:45:53 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 14:46:03 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 14:46:06 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 14:46:06 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 14:46:06 - [32mINFO[0m - Initializing BcryptCryptoProvider[0m
2025-07-30 14:46:06 - [32mINFO[0m - Initializing DatabaseProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='postgres' user=None password=None host=None port=None db_name=None project_name=None postgres_configuration_settings=None default_collection_name='Default' default_collection_description='Your default collection.' collection_summary_system_prompt='system' collection_summary_prompt='collection_summary' disable_create_extension=False batch_size=1 graph_search_results_store_path=None graph_enrichment_settings=GraphEnrichmentSettings(force_graph_search_results_enrichment=False, graph_communities_prompt='graph_communities', max_summary_input_length=65536, generation_config=None, leiden_params={}) graph_creation_settings=GraphCreationSettings(graph_extraction_prompt='graph_extraction', graph_entity_description_prompt='graph_entity_description', entity_types=[], relation_types=[], chunk_merge_count=2, max_knowledge_relationships=100, max_description_input_length=65536, generation_config=None, automatic_deduplication=True) graph_search_settings=GraphSearchSettings(limits={}, enabled=True) limits=LimitSettings(global_per_min=60, route_per_min=20, monthly_limit=10000) maintenance=MaintenanceSettings(vacuum_schedule='0 3 * * *', vacuum_analyze=True, vacuum_full=False) route_limits={} user_limits={}.[0m
2025-07-30 14:46:06 - [32mINFO[0m - Connecting to Postgres via TCP/IP[0m
2025-07-30 14:46:06 - [32mINFO[0m - Initializing `PostgresDatabaseProvider`.[0m
2025-07-30 14:46:06 - [32mINFO[0m - Connecting with 230 connections to `asyncpg.create_pool`.[0m
2025-07-30 14:46:07 - [32mINFO[0m - Successfully connected to Postgres database and created connection pool.[0m
2025-07-30 14:46:07 - [32mINFO[0m - Creating table, if it does not exist: "cscsrag"."documents"[0m
2025-07-30 14:46:09 - [32mINFO[0m - Loading saved prompt: collection_summary[0m
2025-07-30 14:46:09 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent[0m
2025-07-30 14:46:09 - [32mINFO[0m - Loading saved prompt: static_research_agent[0m
2025-07-30 14:46:09 - [32mINFO[0m - Loading saved prompt: summary[0m
2025-07-30 14:46:09 - [32mINFO[0m - Loading saved prompt: vision_pdf[0m
2025-07-30 14:46:09 - [32mINFO[0m - Loading saved prompt: graph_extraction[0m
2025-07-30 14:46:09 - [32mINFO[0m - Loading saved prompt: hyde[0m
2025-07-30 14:46:09 - [32mINFO[0m - Loading saved prompt: rag[0m
2025-07-30 14:46:09 - [32mINFO[0m - Loading saved prompt: rag_fusion[0m
2025-07-30 14:46:09 - [32mINFO[0m - Loading saved prompt: static_rag_agent[0m
2025-07-30 14:46:09 - [32mINFO[0m - Loading saved prompt: system[0m
2025-07-30 14:46:09 - [32mINFO[0m - Loading saved prompt: vision_img[0m
2025-07-30 14:46:09 - [32mINFO[0m - Loading saved prompt: chunk_enrichment[0m
2025-07-30 14:46:09 - [32mINFO[0m - Loading saved prompt: graph_entity_description[0m
2025-07-30 14:46:09 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent_xml_tooling[0m
2025-07-30 14:46:09 - [32mINFO[0m - Loading saved prompt: graph_communities[0m
2025-07-30 14:46:09 - [32mINFO[0m - Loading prompts from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts[0m
2025-07-30 14:46:09 - [32mINFO[0m - Loading default prompt: chunk_enrichment from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\chunk_enrichment.yaml.[0m
2025-07-30 14:46:09 - [32mINFO[0m - Loading default prompt: collection_summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\collection_summary.yaml.[0m
2025-07-30 14:46:09 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\dynamic_rag_agent.yaml.[0m
2025-07-30 14:46:09 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent_xml_tooling from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\dynamic_rag_agent_xml_tooling.yaml.[0m
2025-07-30 14:46:10 - [32mINFO[0m - Loading default prompt: graph_communities from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_communities.yaml.[0m
2025-07-30 14:46:10 - [32mINFO[0m - Loading default prompt: graph_entity_description from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_entity_description.yaml.[0m
2025-07-30 14:46:10 - [32mINFO[0m - Loading default prompt: graph_extraction from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_extraction.yaml.[0m
2025-07-30 14:46:10 - [32mINFO[0m - Loading default prompt: hyde from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\hyde.yaml.[0m
2025-07-30 14:46:10 - [32mINFO[0m - Loading default prompt: rag from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\rag.yaml.[0m
2025-07-30 14:46:10 - [32mINFO[0m - Loading default prompt: rag_fusion from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\rag_fusion.yaml.[0m
2025-07-30 14:46:10 - [32mINFO[0m - Loading default prompt: static_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\static_rag_agent.yaml.[0m
2025-07-30 14:46:11 - [32mINFO[0m - Loading default prompt: static_research_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\static_research_agent.yaml.[0m
2025-07-30 14:46:11 - [32mINFO[0m - Loading default prompt: summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\summary.yaml.[0m
2025-07-30 14:46:11 - [32mINFO[0m - Loading default prompt: system from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\system.yaml.[0m
2025-07-30 14:46:11 - [32mINFO[0m - Loading default prompt: vision_img from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\vision_img.yaml.[0m
2025-07-30 14:46:11 - [32mINFO[0m - Loading default prompt: vision_pdf from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\vision_pdf.yaml.[0m
2025-07-30 14:46:14 - [32mINFO[0m - Using existing S3 bucket: rag[0m
2025-07-30 14:46:14 - [32mINFO[0m - Initializing OCRProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='mistral' model='mistral-ocr-latest' concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0[0m
2025-07-30 14:46:14 - [33mWARNING[0m - MISTRAL_API_KEY not set in environment, if you plan to use Mistral OCR, please set it.[0m
2025-07-30 14:46:17 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:46:17 - [32mINFO[0m - Initialized extra parser zerox for pdf[0m
2025-07-30 14:46:17 - [32mINFO[0m - Initialized extra parser ocr for pdf[0m
2025-07-30 14:46:17 - [32mINFO[0m - R2RIngestionProvider initialized with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' excluded_parsers=[] chunking_strategy=<ChunkingStrategy.RECURSIVE: 'recursive'> chunk_size=1024 chunk_overlap=512 chunk_enrichment_settings=ChunkEnrichmentSettings(enable_chunk_enrichment=False, n_chunks=2, generation_config=None, chunk_enrichment_prompt='chunk_enrichment') extra_parsers={'pdf': ['zerox', 'ocr']} audio_transcription_model=None vlm=None vlm_batch_size=20 vlm_max_tokens_to_sample=1024 max_concurrent_vlm_tasks=20 vlm_ocr_one_page_per_chunk=True skip_document_summary=False document_summary_system_prompt='system' document_summary_task_prompt='summary' chunks_for_document_summary=128 document_summary_model=None parser_overrides={} automatic_extraction=True document_summary_max_length=100000 separator=None[0m
2025-07-30 14:46:17 - [32mINFO[0m - Default admin user already exists.[0m
2025-07-30 14:46:17 - [32mINFO[0m - Initializing database maintenance service[0m
2025-07-30 14:46:17 - [32mINFO[0m - Scheduler started[0m
2025-07-30 14:46:17 - [32mINFO[0m - Scheduler started[0m
2025-07-30 14:46:17 - [32mINFO[0m - Adding job vacuum_database with trigger cron and kwargs {'minute': '0', 'hour': '3', 'day': '*', 'month': '*', 'day_of_week': '*', 'kwargs': {'full': False, 'analyze': True}}[0m
2025-07-30 14:46:17 - [32mINFO[0m - Added job "MaintenanceService.vacuum_database" to job store "default"[0m
2025-07-30 14:46:17 - [32mINFO[0m - Initializing ChunksRouter[0m
2025-07-30 14:46:17 - [32mINFO[0m - Initializing CollectionsRouter[0m
2025-07-30 14:46:17 - [32mINFO[0m - Initializing ConversationsRouter[0m
2025-07-30 14:46:17 - [32mINFO[0m - Initializing DocumentsRouter[0m
2025-07-30 14:46:18 - [32mINFO[0m - Initializing GraphRouter[0m
2025-07-30 14:46:18 - [32mINFO[0m - Initializing IndicesRouter[0m
2025-07-30 14:46:18 - [32mINFO[0m - Initializing PromptsRouter[0m
2025-07-30 14:46:18 - [32mINFO[0m - Initializing RetrievalRouter[0m
2025-07-30 14:46:18 - [32mINFO[0m - Initializing SystemRouter[0m
2025-07-30 14:46:18 - [32mINFO[0m - Initializing UsersRouter[0m
2025-07-30 14:46:19 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 14:46:19 - [32mINFO[0m - Started server process [22376][0m
2025-07-30 14:46:19 - [32mINFO[0m - Waiting for application startup.[0m
2025-07-30 14:46:19 - [32mINFO[0m - Application startup complete.[0m
2025-07-30 14:46:19 - [31mERROR[0m - [Errno 10048] error while attempting to bind on address ('0.0.0.0', 7272): ͨ��ÿ���׽��ֵ�ַ(Э��/�����ַ/�˿�)ֻ����ʹ��һ�Ρ�[0m
2025-07-30 14:46:19 - [32mINFO[0m - Waiting for application shutdown.[0m
2025-07-30 14:46:19 - [32mINFO[0m - Application shutdown complete.[0m
2025-07-30 14:47:43 - [32mINFO[0m - Shutting down[0m
2025-07-30 14:47:43 - [32mINFO[0m - Waiting for application shutdown.[0m
2025-07-30 14:47:43 - [32mINFO[0m - Application shutdown complete.[0m
2025-07-30 14:47:43 - [32mINFO[0m - Finished server process [14624][0m
2025-07-30 14:52:51 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 14:52:51 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://***********:3000/v1'} provider='litellm' base_model='openai/aliyun/text-embedding-v4' base_dimension=512 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 14:52:51 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://***********:3000/v1'} provider='litellm' base_model='openai/aliyun/text-embedding-v4' base_dimension=512 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 14:52:51 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 14:52:51 - [32mINFO[0m - Initializing R2RCompletionProvider...[0m
2025-07-30 14:52:51 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 14:53:14 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 14:53:22 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 14:53:22 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 14:53:22 - [32mINFO[0m - Initializing BcryptCryptoProvider[0m
2025-07-30 14:53:22 - [32mINFO[0m - Initializing DatabaseProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='postgres' user=None password=None host=None port=None db_name=None project_name=None postgres_configuration_settings=None default_collection_name='Default' default_collection_description='Your default collection.' collection_summary_system_prompt='system' collection_summary_prompt='collection_summary' disable_create_extension=False batch_size=1 graph_search_results_store_path=None graph_enrichment_settings=GraphEnrichmentSettings(force_graph_search_results_enrichment=False, graph_communities_prompt='graph_communities', max_summary_input_length=65536, generation_config=None, leiden_params={}) graph_creation_settings=GraphCreationSettings(graph_extraction_prompt='graph_extraction', graph_entity_description_prompt='graph_entity_description', entity_types=[], relation_types=[], chunk_merge_count=2, max_knowledge_relationships=100, max_description_input_length=65536, generation_config=None, automatic_deduplication=True) graph_search_settings=GraphSearchSettings(limits={}, enabled=True) limits=LimitSettings(global_per_min=60, route_per_min=20, monthly_limit=10000) maintenance=MaintenanceSettings(vacuum_schedule='0 3 * * *', vacuum_analyze=True, vacuum_full=False) route_limits={} user_limits={}.[0m
2025-07-30 14:53:22 - [32mINFO[0m - Connecting to Postgres via TCP/IP[0m
2025-07-30 14:53:22 - [32mINFO[0m - Initializing `PostgresDatabaseProvider`.[0m
2025-07-30 14:53:22 - [32mINFO[0m - Connecting with 230 connections to `asyncpg.create_pool`.[0m
2025-07-30 14:53:26 - [32mINFO[0m - Successfully connected to Postgres database and created connection pool.[0m
2025-07-30 14:53:26 - [32mINFO[0m - Creating table, if it does not exist: "cscsrag"."documents"[0m
2025-07-30 14:53:31 - [32mINFO[0m - Loading saved prompt: collection_summary[0m
2025-07-30 14:53:31 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent[0m
2025-07-30 14:53:31 - [32mINFO[0m - Loading saved prompt: vision_pdf[0m
2025-07-30 14:53:31 - [32mINFO[0m - Loading saved prompt: graph_extraction[0m
2025-07-30 14:53:31 - [32mINFO[0m - Loading saved prompt: hyde[0m
2025-07-30 14:53:31 - [32mINFO[0m - Loading saved prompt: rag[0m
2025-07-30 14:53:31 - [32mINFO[0m - Loading saved prompt: rag_fusion[0m
2025-07-30 14:53:31 - [32mINFO[0m - Loading saved prompt: static_rag_agent[0m
2025-07-30 14:53:31 - [32mINFO[0m - Loading saved prompt: static_research_agent[0m
2025-07-30 14:53:31 - [32mINFO[0m - Loading saved prompt: summary[0m
2025-07-30 14:53:31 - [32mINFO[0m - Loading saved prompt: system[0m
2025-07-30 14:53:31 - [32mINFO[0m - Loading saved prompt: vision_img[0m
2025-07-30 14:53:31 - [32mINFO[0m - Loading saved prompt: graph_entity_description[0m
2025-07-30 14:53:31 - [32mINFO[0m - Loading saved prompt: graph_communities[0m
2025-07-30 14:53:31 - [32mINFO[0m - Loading saved prompt: chunk_enrichment[0m
2025-07-30 14:53:31 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent_xml_tooling[0m
2025-07-30 14:53:31 - [32mINFO[0m - Loading prompts from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts[0m
2025-07-30 14:53:31 - [32mINFO[0m - Loading default prompt: chunk_enrichment from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\chunk_enrichment.yaml.[0m
2025-07-30 14:53:31 - [32mINFO[0m - Loading default prompt: collection_summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\collection_summary.yaml.[0m
2025-07-30 14:53:31 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\dynamic_rag_agent.yaml.[0m
2025-07-30 14:53:32 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent_xml_tooling from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\dynamic_rag_agent_xml_tooling.yaml.[0m
2025-07-30 14:53:35 - [32mINFO[0m - Loading default prompt: graph_communities from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_communities.yaml.[0m
2025-07-30 14:53:35 - [32mINFO[0m - Loading default prompt: graph_entity_description from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_entity_description.yaml.[0m
2025-07-30 14:53:35 - [32mINFO[0m - Loading default prompt: graph_extraction from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_extraction.yaml.[0m
2025-07-30 14:53:36 - [32mINFO[0m - Loading default prompt: hyde from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\hyde.yaml.[0m
2025-07-30 14:53:36 - [32mINFO[0m - Loading default prompt: rag from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\rag.yaml.[0m
2025-07-30 14:53:37 - [32mINFO[0m - Loading default prompt: rag_fusion from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\rag_fusion.yaml.[0m
2025-07-30 14:53:38 - [32mINFO[0m - Loading default prompt: static_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\static_rag_agent.yaml.[0m
2025-07-30 14:53:38 - [32mINFO[0m - Loading default prompt: static_research_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\static_research_agent.yaml.[0m
2025-07-30 14:53:38 - [32mINFO[0m - Loading default prompt: summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\summary.yaml.[0m
2025-07-30 14:53:40 - [32mINFO[0m - Loading default prompt: system from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\system.yaml.[0m
2025-07-30 14:53:42 - [32mINFO[0m - Loading default prompt: vision_img from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\vision_img.yaml.[0m
2025-07-30 14:53:42 - [32mINFO[0m - Loading default prompt: vision_pdf from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\vision_pdf.yaml.[0m
2025-07-30 14:53:54 - [32mINFO[0m - Using existing S3 bucket: rag[0m
2025-07-30 14:53:54 - [32mINFO[0m - Initializing OCRProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='mistral' model='mistral-ocr-latest' concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0[0m
2025-07-30 14:53:54 - [33mWARNING[0m - MISTRAL_API_KEY not set in environment, if you plan to use Mistral OCR, please set it.[0m
2025-07-30 14:54:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:54:01 - [32mINFO[0m - Initialized extra parser zerox for pdf[0m
2025-07-30 14:54:01 - [32mINFO[0m - Initialized extra parser ocr for pdf[0m
2025-07-30 14:54:01 - [32mINFO[0m - R2RIngestionProvider initialized with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' excluded_parsers=[] chunking_strategy=<ChunkingStrategy.RECURSIVE: 'recursive'> chunk_size=1024 chunk_overlap=512 chunk_enrichment_settings=ChunkEnrichmentSettings(enable_chunk_enrichment=False, n_chunks=2, generation_config=None, chunk_enrichment_prompt='chunk_enrichment') extra_parsers={'pdf': ['zerox', 'ocr']} audio_transcription_model=None vlm=None vlm_batch_size=20 vlm_max_tokens_to_sample=1024 max_concurrent_vlm_tasks=20 vlm_ocr_one_page_per_chunk=True skip_document_summary=False document_summary_system_prompt='system' document_summary_task_prompt='summary' chunks_for_document_summary=128 document_summary_model=None parser_overrides={} automatic_extraction=True document_summary_max_length=100000 separator=None[0m
2025-07-30 14:54:02 - [32mINFO[0m - Default admin user already exists.[0m
2025-07-30 14:54:02 - [32mINFO[0m - Initializing database maintenance service[0m
2025-07-30 14:54:02 - [32mINFO[0m - Scheduler started[0m
2025-07-30 14:54:02 - [32mINFO[0m - Scheduler started[0m
2025-07-30 14:54:02 - [32mINFO[0m - Adding job vacuum_database with trigger cron and kwargs {'minute': '0', 'hour': '3', 'day': '*', 'month': '*', 'day_of_week': '*', 'kwargs': {'full': False, 'analyze': True}}[0m
2025-07-30 14:54:02 - [32mINFO[0m - Added job "MaintenanceService.vacuum_database" to job store "default"[0m
2025-07-30 14:54:02 - [32mINFO[0m - Initializing ChunksRouter[0m
2025-07-30 14:54:02 - [32mINFO[0m - Initializing CollectionsRouter[0m
2025-07-30 14:54:03 - [32mINFO[0m - Initializing ConversationsRouter[0m
2025-07-30 14:54:03 - [32mINFO[0m - Initializing DocumentsRouter[0m
2025-07-30 14:54:03 - [32mINFO[0m - Initializing GraphRouter[0m
2025-07-30 14:54:03 - [32mINFO[0m - Initializing IndicesRouter[0m
2025-07-30 14:54:04 - [32mINFO[0m - Initializing PromptsRouter[0m
2025-07-30 14:54:04 - [32mINFO[0m - Initializing RetrievalRouter[0m
2025-07-30 14:54:04 - [32mINFO[0m - Initializing SystemRouter[0m
2025-07-30 14:54:04 - [32mINFO[0m - Initializing UsersRouter[0m
2025-07-30 14:54:06 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 14:54:07 - [32mINFO[0m - Started server process [4536][0m
2025-07-30 14:54:07 - [32mINFO[0m - Waiting for application startup.[0m
2025-07-30 14:54:07 - [32mINFO[0m - Application startup complete.[0m
2025-07-30 14:54:07 - [32mINFO[0m - Uvicorn running on http://0.0.0.0:7272 (Press CTRL+C to quit)[0m
2025-07-30 14:55:48 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 14:55:48 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://***********:3000/v1'} provider='litellm' base_model='openai/aliyun/text-embedding-v4' base_dimension=512 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 14:55:48 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://***********:3000/v1'} provider='litellm' base_model='openai/aliyun/text-embedding-v4' base_dimension=512 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 14:55:48 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 14:55:48 - [32mINFO[0m - Initializing R2RCompletionProvider...[0m
2025-07-30 14:55:48 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 14:56:00 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 14:56:08 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 14:56:08 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 14:56:08 - [32mINFO[0m - Initializing BcryptCryptoProvider[0m
2025-07-30 14:56:08 - [32mINFO[0m - Initializing DatabaseProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='postgres' user=None password=None host=None port=None db_name=None project_name=None postgres_configuration_settings=None default_collection_name='Default' default_collection_description='Your default collection.' collection_summary_system_prompt='system' collection_summary_prompt='collection_summary' disable_create_extension=False batch_size=1 graph_search_results_store_path=None graph_enrichment_settings=GraphEnrichmentSettings(force_graph_search_results_enrichment=False, graph_communities_prompt='graph_communities', max_summary_input_length=65536, generation_config=None, leiden_params={}) graph_creation_settings=GraphCreationSettings(graph_extraction_prompt='graph_extraction', graph_entity_description_prompt='graph_entity_description', entity_types=[], relation_types=[], chunk_merge_count=2, max_knowledge_relationships=100, max_description_input_length=65536, generation_config=None, automatic_deduplication=True) graph_search_settings=GraphSearchSettings(limits={}, enabled=True) limits=LimitSettings(global_per_min=60, route_per_min=20, monthly_limit=10000) maintenance=MaintenanceSettings(vacuum_schedule='0 3 * * *', vacuum_analyze=True, vacuum_full=False) route_limits={} user_limits={}.[0m
2025-07-30 14:56:08 - [32mINFO[0m - Connecting to Postgres via TCP/IP[0m
2025-07-30 14:56:08 - [32mINFO[0m - Initializing `PostgresDatabaseProvider`.[0m
2025-07-30 14:56:08 - [32mINFO[0m - Connecting with 230 connections to `asyncpg.create_pool`.[0m
2025-07-30 14:56:11 - [32mINFO[0m - Successfully connected to Postgres database and created connection pool.[0m
2025-07-30 14:56:11 - [32mINFO[0m - Creating table, if it does not exist: "cscsrag"."documents"[0m
2025-07-30 14:56:13 - [32mINFO[0m - Loading saved prompt: collection_summary[0m
2025-07-30 14:56:13 - [32mINFO[0m - Loading saved prompt: graph_extraction[0m
2025-07-30 14:56:13 - [32mINFO[0m - Loading saved prompt: hyde[0m
2025-07-30 14:56:13 - [32mINFO[0m - Loading saved prompt: rag_fusion[0m
2025-07-30 14:56:13 - [32mINFO[0m - Loading saved prompt: static_rag_agent[0m
2025-07-30 14:56:13 - [32mINFO[0m - Loading saved prompt: static_research_agent[0m
2025-07-30 14:56:13 - [32mINFO[0m - Loading saved prompt: summary[0m
2025-07-30 14:56:13 - [32mINFO[0m - Loading saved prompt: system[0m
2025-07-30 14:56:13 - [32mINFO[0m - Loading saved prompt: vision_pdf[0m
2025-07-30 14:56:13 - [32mINFO[0m - Loading saved prompt: rag[0m
2025-07-30 14:56:13 - [32mINFO[0m - Loading saved prompt: vision_img[0m
2025-07-30 14:56:13 - [32mINFO[0m - Loading saved prompt: graph_entity_description[0m
2025-07-30 14:56:13 - [32mINFO[0m - Loading saved prompt: chunk_enrichment[0m
2025-07-30 14:56:13 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent[0m
2025-07-30 14:56:13 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent_xml_tooling[0m
2025-07-30 14:56:13 - [32mINFO[0m - Loading saved prompt: graph_communities[0m
2025-07-30 14:56:13 - [32mINFO[0m - Loading prompts from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts[0m
2025-07-30 14:56:13 - [32mINFO[0m - Loading default prompt: chunk_enrichment from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\chunk_enrichment.yaml.[0m
2025-07-30 14:56:13 - [32mINFO[0m - Loading default prompt: collection_summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\collection_summary.yaml.[0m
2025-07-30 14:56:13 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\dynamic_rag_agent.yaml.[0m
2025-07-30 14:56:13 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent_xml_tooling from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\dynamic_rag_agent_xml_tooling.yaml.[0m
2025-07-30 14:56:13 - [32mINFO[0m - Loading default prompt: graph_communities from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_communities.yaml.[0m
2025-07-30 14:56:13 - [32mINFO[0m - Loading default prompt: graph_entity_description from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_entity_description.yaml.[0m
2025-07-30 14:56:14 - [32mINFO[0m - Loading default prompt: graph_extraction from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_extraction.yaml.[0m
2025-07-30 14:56:14 - [32mINFO[0m - Loading default prompt: hyde from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\hyde.yaml.[0m
2025-07-30 14:56:14 - [32mINFO[0m - Loading default prompt: rag from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\rag.yaml.[0m
2025-07-30 14:56:15 - [32mINFO[0m - Loading default prompt: rag_fusion from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\rag_fusion.yaml.[0m
2025-07-30 14:56:15 - [32mINFO[0m - Loading default prompt: static_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\static_rag_agent.yaml.[0m
2025-07-30 14:56:15 - [32mINFO[0m - Loading default prompt: static_research_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\static_research_agent.yaml.[0m
2025-07-30 14:56:15 - [32mINFO[0m - Loading default prompt: summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\summary.yaml.[0m
2025-07-30 14:56:15 - [32mINFO[0m - Loading default prompt: system from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\system.yaml.[0m
2025-07-30 14:56:15 - [32mINFO[0m - Loading default prompt: vision_img from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\vision_img.yaml.[0m
2025-07-30 14:56:16 - [32mINFO[0m - Loading default prompt: vision_pdf from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\vision_pdf.yaml.[0m
2025-07-30 14:56:21 - [32mINFO[0m - Using existing S3 bucket: rag[0m
2025-07-30 14:56:21 - [32mINFO[0m - Initializing OCRProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='mistral' model='mistral-ocr-latest' concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0[0m
2025-07-30 14:56:21 - [33mWARNING[0m - MISTRAL_API_KEY not set in environment, if you plan to use Mistral OCR, please set it.[0m
2025-07-30 14:56:24 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 14:56:24 - [32mINFO[0m - Initialized extra parser zerox for pdf[0m
2025-07-30 14:56:24 - [32mINFO[0m - Initialized extra parser ocr for pdf[0m
2025-07-30 14:56:24 - [32mINFO[0m - R2RIngestionProvider initialized with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' excluded_parsers=[] chunking_strategy=<ChunkingStrategy.RECURSIVE: 'recursive'> chunk_size=1024 chunk_overlap=512 chunk_enrichment_settings=ChunkEnrichmentSettings(enable_chunk_enrichment=False, n_chunks=2, generation_config=None, chunk_enrichment_prompt='chunk_enrichment') extra_parsers={'pdf': ['zerox', 'ocr']} audio_transcription_model=None vlm=None vlm_batch_size=20 vlm_max_tokens_to_sample=1024 max_concurrent_vlm_tasks=20 vlm_ocr_one_page_per_chunk=True skip_document_summary=False document_summary_system_prompt='system' document_summary_task_prompt='summary' chunks_for_document_summary=128 document_summary_model=None parser_overrides={} automatic_extraction=True document_summary_max_length=100000 separator=None[0m
2025-07-30 14:56:24 - [32mINFO[0m - Default admin user already exists.[0m
2025-07-30 14:56:24 - [32mINFO[0m - Initializing database maintenance service[0m
2025-07-30 14:56:24 - [32mINFO[0m - Scheduler started[0m
2025-07-30 14:56:24 - [32mINFO[0m - Scheduler started[0m
2025-07-30 14:56:24 - [32mINFO[0m - Adding job vacuum_database with trigger cron and kwargs {'minute': '0', 'hour': '3', 'day': '*', 'month': '*', 'day_of_week': '*', 'kwargs': {'full': False, 'analyze': True}}[0m
2025-07-30 14:56:24 - [32mINFO[0m - Added job "MaintenanceService.vacuum_database" to job store "default"[0m
2025-07-30 14:56:24 - [32mINFO[0m - Initializing ChunksRouter[0m
2025-07-30 14:56:24 - [32mINFO[0m - Initializing CollectionsRouter[0m
2025-07-30 14:56:24 - [32mINFO[0m - Initializing ConversationsRouter[0m
2025-07-30 14:56:24 - [32mINFO[0m - Initializing DocumentsRouter[0m
2025-07-30 14:56:24 - [32mINFO[0m - Initializing GraphRouter[0m
2025-07-30 14:56:25 - [32mINFO[0m - Initializing IndicesRouter[0m
2025-07-30 14:56:25 - [32mINFO[0m - Initializing PromptsRouter[0m
2025-07-30 14:56:25 - [32mINFO[0m - Initializing RetrievalRouter[0m
2025-07-30 14:56:25 - [32mINFO[0m - Initializing SystemRouter[0m
2025-07-30 14:56:25 - [32mINFO[0m - Initializing UsersRouter[0m
2025-07-30 14:56:26 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 14:56:26 - [32mINFO[0m - Started server process [32264][0m
2025-07-30 14:56:26 - [32mINFO[0m - Waiting for application startup.[0m
2025-07-30 14:56:26 - [32mINFO[0m - Application startup complete.[0m
2025-07-30 14:56:26 - [31mERROR[0m - [Errno 10048] error while attempting to bind on address ('0.0.0.0', 7272): ͨ��ÿ���׽��ֵ�ַ(Э��/�����ַ/�˿�)ֻ����ʹ��һ�Ρ�[0m
2025-07-30 14:56:26 - [32mINFO[0m - Waiting for application shutdown.[0m
2025-07-30 14:56:26 - [32mINFO[0m - Application shutdown complete.[0m
2025-07-30 14:56:46 - [33mWARNING[0m - 127.0.0.1:51771 - "POST /v3/retrieval/embedding HTTP/1.1" [33m422[0m
2025-07-30 14:57:18 - [33mWARNING[0m - 127.0.0.1:51886 - "POST /v3/retrieval/embedding HTTP/1.1" [33m422[0m
2025-07-30 14:58:20 - [33mWARNING[0m - 127.0.0.1:52035 - "POST /v3/retrieval/embedding HTTP/1.1" [33m422[0m
2025-07-30 14:59:05 - [33mWARNING[0m - 127.0.0.1:52125 - "POST /v3/retrieval/embedding HTTP/1.1" [33m422[0m
2025-07-30 15:00:12 - [33mWARNING[0m - 127.0.0.1:52368 - "GET / HTTP/1.1" [33m404[0m
2025-07-30 15:00:14 - [33mWARNING[0m - 127.0.0.1:52368 - "GET /favicon.ico HTTP/1.1" [33m404[0m
2025-07-30 15:00:46 - [32mINFO[0m - Embedding request kwargs: {'model': 'openai/aliyun/text-embedding-v4', 'dimensions': 512, 'api_base': 'http://***********:3000/v1', 'api_key': 'sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI'}[0m
2025-07-30 15:00:46 - [33mWARNING[0m - Failed to truncate texts: This model isn't mapped yet. model=openai/aliyun/text-embedding-v4, custom_llm_provider=openai. Add it here - https://github.com/BerriAI/litellm/blob/main/model_prices_and_context_window.json.[0m
2025-07-30 15:00:46 - [31mERROR[0m - Error getting embeddings: litellm.UnsupportedParamsError: Setting dimensions is not supported for OpenAI `text-embedding-3` and later models. To drop it from the call, set `litellm.drop_params = True`.[0m
2025-07-30 15:00:46 - [33mWARNING[0m - Request failed (attempt 1): Error getting embeddings: litellm.UnsupportedParamsError: Setting dimensions is not supported for OpenAI `text-embedding-3` and later models. To drop it from the call, set `litellm.drop_params = True`.[0m
2025-07-30 15:00:47 - [32mINFO[0m - Embedding request kwargs: {'model': 'openai/aliyun/text-embedding-v4', 'dimensions': 512, 'api_base': 'http://***********:3000/v1', 'api_key': 'sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI'}[0m
2025-07-30 15:00:47 - [33mWARNING[0m - Failed to truncate texts: This model isn't mapped yet. model=openai/aliyun/text-embedding-v4, custom_llm_provider=openai. Add it here - https://github.com/BerriAI/litellm/blob/main/model_prices_and_context_window.json.[0m
2025-07-30 15:00:47 - [31mERROR[0m - Error getting embeddings: litellm.UnsupportedParamsError: Setting dimensions is not supported for OpenAI `text-embedding-3` and later models. To drop it from the call, set `litellm.drop_params = True`.[0m
2025-07-30 15:00:47 - [33mWARNING[0m - Request failed (attempt 2): Error getting embeddings: litellm.UnsupportedParamsError: Setting dimensions is not supported for OpenAI `text-embedding-3` and later models. To drop it from the call, set `litellm.drop_params = True`.[0m
2025-07-30 15:00:47 - [32mINFO[0m - Embedding request kwargs: {'model': 'openai/aliyun/text-embedding-v4', 'dimensions': 512, 'api_base': 'http://***********:3000/v1', 'api_key': 'sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI'}[0m
2025-07-30 15:00:47 - [33mWARNING[0m - Failed to truncate texts: This model isn't mapped yet. model=openai/aliyun/text-embedding-v4, custom_llm_provider=openai. Add it here - https://github.com/BerriAI/litellm/blob/main/model_prices_and_context_window.json.[0m
2025-07-30 15:00:47 - [31mERROR[0m - Error getting embeddings: litellm.UnsupportedParamsError: Setting dimensions is not supported for OpenAI `text-embedding-3` and later models. To drop it from the call, set `litellm.drop_params = True`.[0m
2025-07-30 15:00:47 - [33mWARNING[0m - Request failed (attempt 3): Error getting embeddings: litellm.UnsupportedParamsError: Setting dimensions is not supported for OpenAI `text-embedding-3` and later models. To drop it from the call, set `litellm.drop_params = True`.[0m
2025-07-30 15:00:47 - [33mWARNING[0m - 127.0.0.1:52442 - "POST /v3/retrieval/embedding HTTP/1.1" [33m400[0m
2025-07-30 15:01:20 - [33mWARNING[0m - 127.0.0.1:52508 - "GET /health HTTP/1.1" [33m404[0m
2025-07-30 15:03:47 - [33mWARNING[0m - 127.0.0.1:52946 - "POST /v3/retrieval/embedding HTTP/1.1" [33m422[0m
2025-07-30 15:03:47 - [33mWARNING[0m - 127.0.0.1:52946 - "POST /v3/retrieval/embedding HTTP/1.1" [33m422[0m
2025-07-30 15:03:47 - [32mINFO[0m - Embedding request kwargs: {'model': 'openai/aliyun/text-embedding-v4', 'dimensions': 512, 'api_base': 'http://***********:3000/v1', 'api_key': 'sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI'}[0m
2025-07-30 15:03:47 - [33mWARNING[0m - Failed to truncate texts: This model isn't mapped yet. model=openai/aliyun/text-embedding-v4, custom_llm_provider=openai. Add it here - https://github.com/BerriAI/litellm/blob/main/model_prices_and_context_window.json.[0m
2025-07-30 15:03:47 - [31mERROR[0m - Error getting embeddings: litellm.UnsupportedParamsError: Setting dimensions is not supported for OpenAI `text-embedding-3` and later models. To drop it from the call, set `litellm.drop_params = True`.[0m
2025-07-30 15:03:47 - [33mWARNING[0m - Request failed (attempt 1): Error getting embeddings: litellm.UnsupportedParamsError: Setting dimensions is not supported for OpenAI `text-embedding-3` and later models. To drop it from the call, set `litellm.drop_params = True`.[0m
2025-07-30 15:03:48 - [32mINFO[0m - Embedding request kwargs: {'model': 'openai/aliyun/text-embedding-v4', 'dimensions': 512, 'api_base': 'http://***********:3000/v1', 'api_key': 'sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI'}[0m
2025-07-30 15:03:48 - [33mWARNING[0m - Failed to truncate texts: This model isn't mapped yet. model=openai/aliyun/text-embedding-v4, custom_llm_provider=openai. Add it here - https://github.com/BerriAI/litellm/blob/main/model_prices_and_context_window.json.[0m
2025-07-30 15:03:48 - [31mERROR[0m - Error getting embeddings: litellm.UnsupportedParamsError: Setting dimensions is not supported for OpenAI `text-embedding-3` and later models. To drop it from the call, set `litellm.drop_params = True`.[0m
2025-07-30 15:03:48 - [33mWARNING[0m - Request failed (attempt 2): Error getting embeddings: litellm.UnsupportedParamsError: Setting dimensions is not supported for OpenAI `text-embedding-3` and later models. To drop it from the call, set `litellm.drop_params = True`.[0m
2025-07-30 15:03:48 - [32mINFO[0m - Embedding request kwargs: {'model': 'openai/aliyun/text-embedding-v4', 'dimensions': 512, 'api_base': 'http://***********:3000/v1', 'api_key': 'sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI'}[0m
2025-07-30 15:03:48 - [33mWARNING[0m - Failed to truncate texts: This model isn't mapped yet. model=openai/aliyun/text-embedding-v4, custom_llm_provider=openai. Add it here - https://github.com/BerriAI/litellm/blob/main/model_prices_and_context_window.json.[0m
2025-07-30 15:03:48 - [31mERROR[0m - Error getting embeddings: litellm.UnsupportedParamsError: Setting dimensions is not supported for OpenAI `text-embedding-3` and later models. To drop it from the call, set `litellm.drop_params = True`.[0m
2025-07-30 15:03:48 - [33mWARNING[0m - Request failed (attempt 3): Error getting embeddings: litellm.UnsupportedParamsError: Setting dimensions is not supported for OpenAI `text-embedding-3` and later models. To drop it from the call, set `litellm.drop_params = True`.[0m
2025-07-30 15:03:48 - [33mWARNING[0m - 127.0.0.1:52946 - "POST /v3/retrieval/embedding HTTP/1.1" [33m400[0m
2025-07-30 15:05:33 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 15:05:33 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://***********:3000/v1'} provider='litellm' base_model='openai/aliyun/text-embedding-v4' base_dimension=512 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 15:05:33 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://***********:3000/v1'} provider='litellm' base_model='openai/aliyun/text-embedding-v4' base_dimension=512 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 15:05:33 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:05:33 - [32mINFO[0m - Initializing R2RCompletionProvider...[0m
2025-07-30 15:05:33 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:05:44 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:05:47 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:05:47 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:05:47 - [32mINFO[0m - Initializing BcryptCryptoProvider[0m
2025-07-30 15:05:47 - [32mINFO[0m - Initializing DatabaseProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='postgres' user=None password=None host=None port=None db_name=None project_name=None postgres_configuration_settings=None default_collection_name='Default' default_collection_description='Your default collection.' collection_summary_system_prompt='system' collection_summary_prompt='collection_summary' disable_create_extension=False batch_size=1 graph_search_results_store_path=None graph_enrichment_settings=GraphEnrichmentSettings(force_graph_search_results_enrichment=False, graph_communities_prompt='graph_communities', max_summary_input_length=65536, generation_config=None, leiden_params={}) graph_creation_settings=GraphCreationSettings(graph_extraction_prompt='graph_extraction', graph_entity_description_prompt='graph_entity_description', entity_types=[], relation_types=[], chunk_merge_count=2, max_knowledge_relationships=100, max_description_input_length=65536, generation_config=None, automatic_deduplication=True) graph_search_settings=GraphSearchSettings(limits={}, enabled=True) limits=LimitSettings(global_per_min=60, route_per_min=20, monthly_limit=10000) maintenance=MaintenanceSettings(vacuum_schedule='0 3 * * *', vacuum_analyze=True, vacuum_full=False) route_limits={} user_limits={}.[0m
2025-07-30 15:05:47 - [32mINFO[0m - Connecting to Postgres via TCP/IP[0m
2025-07-30 15:05:47 - [32mINFO[0m - Initializing `PostgresDatabaseProvider`.[0m
2025-07-30 15:05:47 - [32mINFO[0m - Connecting with 230 connections to `asyncpg.create_pool`.[0m
2025-07-30 15:05:49 - [32mINFO[0m - Successfully connected to Postgres database and created connection pool.[0m
2025-07-30 15:05:49 - [32mINFO[0m - Creating table, if it does not exist: "cscsrag"."documents"[0m
2025-07-30 15:05:51 - [32mINFO[0m - Loading saved prompt: collection_summary[0m
2025-07-30 15:05:51 - [32mINFO[0m - Loading saved prompt: graph_extraction[0m
2025-07-30 15:05:51 - [32mINFO[0m - Loading saved prompt: rag_fusion[0m
2025-07-30 15:05:51 - [32mINFO[0m - Loading saved prompt: static_rag_agent[0m
2025-07-30 15:05:51 - [32mINFO[0m - Loading saved prompt: static_research_agent[0m
2025-07-30 15:05:51 - [32mINFO[0m - Loading saved prompt: summary[0m
2025-07-30 15:05:51 - [32mINFO[0m - Loading saved prompt: vision_pdf[0m
2025-07-30 15:05:51 - [32mINFO[0m - Loading saved prompt: hyde[0m
2025-07-30 15:05:51 - [32mINFO[0m - Loading saved prompt: rag[0m
2025-07-30 15:05:51 - [32mINFO[0m - Loading saved prompt: system[0m
2025-07-30 15:05:51 - [32mINFO[0m - Loading saved prompt: vision_img[0m
2025-07-30 15:05:51 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent[0m
2025-07-30 15:05:51 - [32mINFO[0m - Loading saved prompt: graph_communities[0m
2025-07-30 15:05:51 - [32mINFO[0m - Loading saved prompt: chunk_enrichment[0m
2025-07-30 15:05:51 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent_xml_tooling[0m
2025-07-30 15:05:51 - [32mINFO[0m - Loading saved prompt: graph_entity_description[0m
2025-07-30 15:05:51 - [32mINFO[0m - Loading prompts from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts[0m
2025-07-30 15:05:51 - [32mINFO[0m - Loading default prompt: chunk_enrichment from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\chunk_enrichment.yaml.[0m
2025-07-30 15:05:51 - [32mINFO[0m - Loading default prompt: collection_summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\collection_summary.yaml.[0m
2025-07-30 15:05:51 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\dynamic_rag_agent.yaml.[0m
2025-07-30 15:05:51 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent_xml_tooling from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\dynamic_rag_agent_xml_tooling.yaml.[0m
2025-07-30 15:05:51 - [32mINFO[0m - Loading default prompt: graph_communities from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_communities.yaml.[0m
2025-07-30 15:05:51 - [32mINFO[0m - Loading default prompt: graph_entity_description from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_entity_description.yaml.[0m
2025-07-30 15:05:52 - [32mINFO[0m - Loading default prompt: graph_extraction from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_extraction.yaml.[0m
2025-07-30 15:05:52 - [32mINFO[0m - Loading default prompt: hyde from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\hyde.yaml.[0m
2025-07-30 15:05:52 - [32mINFO[0m - Loading default prompt: rag from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\rag.yaml.[0m
2025-07-30 15:05:52 - [32mINFO[0m - Loading default prompt: rag_fusion from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\rag_fusion.yaml.[0m
2025-07-30 15:05:52 - [32mINFO[0m - Loading default prompt: static_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\static_rag_agent.yaml.[0m
2025-07-30 15:05:52 - [32mINFO[0m - Loading default prompt: static_research_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\static_research_agent.yaml.[0m
2025-07-30 15:05:52 - [32mINFO[0m - Loading default prompt: summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\summary.yaml.[0m
2025-07-30 15:05:53 - [32mINFO[0m - Loading default prompt: system from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\system.yaml.[0m
2025-07-30 15:05:53 - [32mINFO[0m - Loading default prompt: vision_img from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\vision_img.yaml.[0m
2025-07-30 15:05:53 - [32mINFO[0m - Loading default prompt: vision_pdf from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\vision_pdf.yaml.[0m
2025-07-30 15:05:56 - [32mINFO[0m - Using existing S3 bucket: rag[0m
2025-07-30 15:05:56 - [32mINFO[0m - Initializing OCRProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='mistral' model='mistral-ocr-latest' concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0[0m
2025-07-30 15:05:56 - [33mWARNING[0m - MISTRAL_API_KEY not set in environment, if you plan to use Mistral OCR, please set it.[0m
2025-07-30 15:05:59 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 15:05:59 - [32mINFO[0m - Initialized extra parser zerox for pdf[0m
2025-07-30 15:05:59 - [32mINFO[0m - Initialized extra parser ocr for pdf[0m
2025-07-30 15:05:59 - [32mINFO[0m - R2RIngestionProvider initialized with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' excluded_parsers=[] chunking_strategy=<ChunkingStrategy.RECURSIVE: 'recursive'> chunk_size=1024 chunk_overlap=512 chunk_enrichment_settings=ChunkEnrichmentSettings(enable_chunk_enrichment=False, n_chunks=2, generation_config=None, chunk_enrichment_prompt='chunk_enrichment') extra_parsers={'pdf': ['zerox', 'ocr']} audio_transcription_model=None vlm=None vlm_batch_size=20 vlm_max_tokens_to_sample=1024 max_concurrent_vlm_tasks=20 vlm_ocr_one_page_per_chunk=True skip_document_summary=False document_summary_system_prompt='system' document_summary_task_prompt='summary' chunks_for_document_summary=128 document_summary_model=None parser_overrides={} automatic_extraction=True document_summary_max_length=100000 separator=None[0m
2025-07-30 15:05:59 - [32mINFO[0m - Default admin user already exists.[0m
2025-07-30 15:05:59 - [32mINFO[0m - Initializing database maintenance service[0m
2025-07-30 15:05:59 - [32mINFO[0m - Scheduler started[0m
2025-07-30 15:05:59 - [32mINFO[0m - Scheduler started[0m
2025-07-30 15:05:59 - [32mINFO[0m - Adding job vacuum_database with trigger cron and kwargs {'minute': '0', 'hour': '3', 'day': '*', 'month': '*', 'day_of_week': '*', 'kwargs': {'full': False, 'analyze': True}}[0m
2025-07-30 15:05:59 - [32mINFO[0m - Added job "MaintenanceService.vacuum_database" to job store "default"[0m
2025-07-30 15:05:59 - [32mINFO[0m - Initializing ChunksRouter[0m
2025-07-30 15:05:59 - [32mINFO[0m - Initializing CollectionsRouter[0m
2025-07-30 15:06:00 - [32mINFO[0m - Initializing ConversationsRouter[0m
2025-07-30 15:06:00 - [32mINFO[0m - Initializing DocumentsRouter[0m
2025-07-30 15:06:00 - [32mINFO[0m - Initializing GraphRouter[0m
2025-07-30 15:06:00 - [32mINFO[0m - Initializing IndicesRouter[0m
2025-07-30 15:06:00 - [32mINFO[0m - Initializing PromptsRouter[0m
2025-07-30 15:06:00 - [32mINFO[0m - Initializing RetrievalRouter[0m
2025-07-30 15:06:00 - [32mINFO[0m - Initializing SystemRouter[0m
2025-07-30 15:06:00 - [32mINFO[0m - Initializing UsersRouter[0m
2025-07-30 15:06:02 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 15:06:02 - [32mINFO[0m - Started server process [34092][0m
2025-07-30 15:06:02 - [32mINFO[0m - Waiting for application startup.[0m
2025-07-30 15:06:02 - [32mINFO[0m - Application startup complete.[0m
2025-07-30 15:06:02 - [31mERROR[0m - [Errno 10048] error while attempting to bind on address ('0.0.0.0', 7272): ͨ��ÿ���׽��ֵ�ַ(Э��/�����ַ/�˿�)ֻ����ʹ��һ�Ρ�[0m
2025-07-30 15:06:02 - [32mINFO[0m - Waiting for application shutdown.[0m
2025-07-30 15:06:02 - [32mINFO[0m - Application shutdown complete.[0m
2025-07-30 15:17:54 - [32mINFO[0m - Shutting down[0m
2025-07-30 15:17:54 - [32mINFO[0m - Waiting for application shutdown.[0m
2025-07-30 15:17:54 - [32mINFO[0m - Application shutdown complete.[0m
2025-07-30 15:17:54 - [32mINFO[0m - Finished server process [4536][0m
2025-07-30 15:21:59 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 15:21:59 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://***********:3000/v1'} provider='newapi' base_model='aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 15:21:59 - [32mINFO[0m - Initialized NewAPIEmbeddingProvider with model: aliyun/text-embedding-v4[0m
2025-07-30 15:21:59 - [32mINFO[0m - API Base: http://***********:3000/v1[0m
2025-07-30 15:21:59 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://***********:3000/v1'} provider='newapi' base_model='aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 15:21:59 - [32mINFO[0m - Initialized NewAPIEmbeddingProvider with model: aliyun/text-embedding-v4[0m
2025-07-30 15:21:59 - [32mINFO[0m - API Base: http://***********:3000/v1[0m
2025-07-30 15:21:59 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:21:59 - [32mINFO[0m - Initializing R2RCompletionProvider...[0m
2025-07-30 15:21:59 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:22:09 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:22:13 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:22:13 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:22:13 - [32mINFO[0m - Initializing BcryptCryptoProvider[0m
2025-07-30 15:22:13 - [32mINFO[0m - Initializing DatabaseProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='postgres' user=None password=None host=None port=None db_name=None project_name=None postgres_configuration_settings=None default_collection_name='Default' default_collection_description='Your default collection.' collection_summary_system_prompt='system' collection_summary_prompt='collection_summary' disable_create_extension=False batch_size=1 graph_search_results_store_path=None graph_enrichment_settings=GraphEnrichmentSettings(force_graph_search_results_enrichment=False, graph_communities_prompt='graph_communities', max_summary_input_length=65536, generation_config=None, leiden_params={}) graph_creation_settings=GraphCreationSettings(graph_extraction_prompt='graph_extraction', graph_entity_description_prompt='graph_entity_description', entity_types=[], relation_types=[], chunk_merge_count=2, max_knowledge_relationships=100, max_description_input_length=65536, generation_config=None, automatic_deduplication=True) graph_search_settings=GraphSearchSettings(limits={}, enabled=True) limits=LimitSettings(global_per_min=60, route_per_min=20, monthly_limit=10000) maintenance=MaintenanceSettings(vacuum_schedule='0 3 * * *', vacuum_analyze=True, vacuum_full=False) route_limits={} user_limits={}.[0m
2025-07-30 15:22:13 - [32mINFO[0m - Connecting to Postgres via TCP/IP[0m
2025-07-30 15:22:13 - [32mINFO[0m - Initializing `PostgresDatabaseProvider`.[0m
2025-07-30 15:22:13 - [32mINFO[0m - Connecting with 230 connections to `asyncpg.create_pool`.[0m
2025-07-30 15:22:15 - [32mINFO[0m - Successfully connected to Postgres database and created connection pool.[0m
2025-07-30 15:22:15 - [32mINFO[0m - Creating table, if it does not exist: "cscsrag"."documents"[0m
2025-07-30 15:22:16 - [31mERROR[0m - Error Dimension mismatch: Table 'cscsrag.chunks' was created with dimension 512, but 1024 was provided. You must use the same dimension for existing tables. while creating R2RProviders.[0m
2025-07-30 15:22:16 - [31mERROR[0m - Failed to start R2R server: Dimension mismatch: Table 'cscsrag.chunks' was created with dimension 512, but 1024 was provided. You must use the same dimension for existing tables.[0m
2025-07-30 15:29:30 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 15:29:30 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://***********:3000/v1'} provider='newapi' base_model='aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 15:29:30 - [32mINFO[0m - Initialized NewAPIEmbeddingProvider with model: aliyun/text-embedding-v4[0m
2025-07-30 15:29:30 - [32mINFO[0m - API Base: http://***********:3000/v1[0m
2025-07-30 15:29:30 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://***********:3000/v1'} provider='newapi' base_model='aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 15:29:30 - [32mINFO[0m - Initialized NewAPIEmbeddingProvider with model: aliyun/text-embedding-v4[0m
2025-07-30 15:29:30 - [32mINFO[0m - API Base: http://***********:3000/v1[0m
2025-07-30 15:29:30 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:29:30 - [32mINFO[0m - Initializing R2RCompletionProvider...[0m
2025-07-30 15:29:30 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:29:42 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:29:47 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:29:47 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:29:47 - [32mINFO[0m - Initializing BcryptCryptoProvider[0m
2025-07-30 15:29:47 - [32mINFO[0m - Initializing DatabaseProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='postgres' user=None password=None host=None port=None db_name=None project_name=None postgres_configuration_settings=None default_collection_name='Default' default_collection_description='Your default collection.' collection_summary_system_prompt='system' collection_summary_prompt='collection_summary' disable_create_extension=False batch_size=1 graph_search_results_store_path=None graph_enrichment_settings=GraphEnrichmentSettings(force_graph_search_results_enrichment=False, graph_communities_prompt='graph_communities', max_summary_input_length=65536, generation_config=None, leiden_params={}) graph_creation_settings=GraphCreationSettings(graph_extraction_prompt='graph_extraction', graph_entity_description_prompt='graph_entity_description', entity_types=[], relation_types=[], chunk_merge_count=2, max_knowledge_relationships=100, max_description_input_length=65536, generation_config=None, automatic_deduplication=True) graph_search_settings=GraphSearchSettings(limits={}, enabled=True) limits=LimitSettings(global_per_min=60, route_per_min=20, monthly_limit=10000) maintenance=MaintenanceSettings(vacuum_schedule='0 3 * * *', vacuum_analyze=True, vacuum_full=False) route_limits={} user_limits={}.[0m
2025-07-30 15:29:47 - [32mINFO[0m - Connecting to Postgres via TCP/IP[0m
2025-07-30 15:29:47 - [32mINFO[0m - Initializing `PostgresDatabaseProvider`.[0m
2025-07-30 15:29:47 - [32mINFO[0m - Connecting with 230 connections to `asyncpg.create_pool`.[0m
2025-07-30 15:33:57 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 15:33:57 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://***********:3000/v1'} provider='newapi' base_model='aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 15:33:57 - [32mINFO[0m - Initialized NewAPIEmbeddingProvider with model: aliyun/text-embedding-v4[0m
2025-07-30 15:33:57 - [32mINFO[0m - API Base: http://***********:3000/v1[0m
2025-07-30 15:33:57 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://***********:3000/v1'} provider='newapi' base_model='aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 15:33:57 - [32mINFO[0m - Initialized NewAPIEmbeddingProvider with model: aliyun/text-embedding-v4[0m
2025-07-30 15:33:57 - [32mINFO[0m - API Base: http://***********:3000/v1[0m
2025-07-30 15:33:57 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:33:57 - [32mINFO[0m - Initializing R2RCompletionProvider...[0m
2025-07-30 15:33:57 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:34:07 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:34:10 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:34:10 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:34:10 - [32mINFO[0m - Initializing BcryptCryptoProvider[0m
2025-07-30 15:34:10 - [32mINFO[0m - Initializing DatabaseProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='postgres' user=None password=None host=None port=None db_name=None project_name=None postgres_configuration_settings=None default_collection_name='Default' default_collection_description='Your default collection.' collection_summary_system_prompt='system' collection_summary_prompt='collection_summary' disable_create_extension=False batch_size=1 graph_search_results_store_path=None graph_enrichment_settings=GraphEnrichmentSettings(force_graph_search_results_enrichment=False, graph_communities_prompt='graph_communities', max_summary_input_length=65536, generation_config=None, leiden_params={}) graph_creation_settings=GraphCreationSettings(graph_extraction_prompt='graph_extraction', graph_entity_description_prompt='graph_entity_description', entity_types=[], relation_types=[], chunk_merge_count=2, max_knowledge_relationships=100, max_description_input_length=65536, generation_config=None, automatic_deduplication=True) graph_search_settings=GraphSearchSettings(limits={}, enabled=True) limits=LimitSettings(global_per_min=60, route_per_min=20, monthly_limit=10000) maintenance=MaintenanceSettings(vacuum_schedule='0 3 * * *', vacuum_analyze=True, vacuum_full=False) route_limits={} user_limits={}.[0m
2025-07-30 15:34:10 - [32mINFO[0m - Connecting to Postgres via TCP/IP[0m
2025-07-30 15:34:10 - [32mINFO[0m - Initializing `PostgresDatabaseProvider`.[0m
2025-07-30 15:34:10 - [32mINFO[0m - Connecting with 230 connections to `asyncpg.create_pool`.[0m
2025-07-30 15:34:11 - [32mINFO[0m - Successfully connected to Postgres database and created connection pool.[0m
2025-07-30 15:34:11 - [32mINFO[0m - Creating table, if it does not exist: "cscsrag"."documents"[0m
2025-07-30 15:34:12 - [31mERROR[0m - Error Dimension mismatch: Table 'cscsrag.chunks' was created with dimension 512, but 1024 was provided. You must use the same dimension for existing tables. while creating R2RProviders.[0m
2025-07-30 15:34:12 - [31mERROR[0m - Failed to start R2R server: Dimension mismatch: Table 'cscsrag.chunks' was created with dimension 512, but 1024 was provided. You must use the same dimension for existing tables.[0m
2025-07-30 15:41:51 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 15:41:51 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://***********:3000/v1'} provider='newapi' base_model='aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 15:41:51 - [32mINFO[0m - Initialized NewAPIEmbeddingProvider with model: aliyun/text-embedding-v4[0m
2025-07-30 15:41:51 - [32mINFO[0m - API Base: http://***********:3000/v1[0m
2025-07-30 15:41:51 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://***********:3000/v1'} provider='newapi' base_model='aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 15:41:51 - [32mINFO[0m - Initialized NewAPIEmbeddingProvider with model: aliyun/text-embedding-v4[0m
2025-07-30 15:41:51 - [32mINFO[0m - API Base: http://***********:3000/v1[0m
2025-07-30 15:41:51 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:41:51 - [32mINFO[0m - Initializing R2RCompletionProvider...[0m
2025-07-30 15:41:51 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:42:33 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:42:38 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:42:38 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:42:38 - [32mINFO[0m - Initializing BcryptCryptoProvider[0m
2025-07-30 15:42:38 - [32mINFO[0m - Initializing DatabaseProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='postgres' user=None password=None host=None port=None db_name=None project_name=None postgres_configuration_settings=None default_collection_name='Default' default_collection_description='Your default collection.' collection_summary_system_prompt='system' collection_summary_prompt='collection_summary' disable_create_extension=False batch_size=1 graph_search_results_store_path=None graph_enrichment_settings=GraphEnrichmentSettings(force_graph_search_results_enrichment=False, graph_communities_prompt='graph_communities', max_summary_input_length=65536, generation_config=None, leiden_params={}) graph_creation_settings=GraphCreationSettings(graph_extraction_prompt='graph_extraction', graph_entity_description_prompt='graph_entity_description', entity_types=[], relation_types=[], chunk_merge_count=2, max_knowledge_relationships=100, max_description_input_length=65536, generation_config=None, automatic_deduplication=True) graph_search_settings=GraphSearchSettings(limits={}, enabled=True) limits=LimitSettings(global_per_min=60, route_per_min=20, monthly_limit=10000) maintenance=MaintenanceSettings(vacuum_schedule='0 3 * * *', vacuum_analyze=True, vacuum_full=False) route_limits={} user_limits={}.[0m
2025-07-30 15:42:38 - [32mINFO[0m - Connecting to Postgres via TCP/IP[0m
2025-07-30 15:42:38 - [32mINFO[0m - Initializing `PostgresDatabaseProvider`.[0m
2025-07-30 15:42:38 - [32mINFO[0m - Connecting with 230 connections to `asyncpg.create_pool`.[0m
2025-07-30 15:42:39 - [32mINFO[0m - Successfully connected to Postgres database and created connection pool.[0m
2025-07-30 15:42:39 - [32mINFO[0m - Creating table, if it does not exist: "cscsrag"."documents"[0m
2025-07-30 15:42:40 - [33mWARNING[0m - Dimension mismatch detected: Table 'cscsrag.documents' summary_embedding has dimension 512, but 1024 is required. Recreating table with new dimension...[0m
2025-07-30 15:42:40 - [32mINFO[0m - Dropped existing table cscsrag.documents[0m
2025-07-30 15:42:41 - [33mWARNING[0m - Dimension mismatch detected: Table 'cscsrag.chunks' has dimension 512, but 1024 is required. Recreating table with new dimension...[0m
2025-07-30 15:42:41 - [32mINFO[0m - Dropped existing table cscsrag.chunks[0m
2025-07-30 15:42:42 - [32mINFO[0m - Loading saved prompt: collection_summary[0m
2025-07-30 15:42:42 - [32mINFO[0m - Loading saved prompt: rag_fusion[0m
2025-07-30 15:42:42 - [32mINFO[0m - Loading saved prompt: static_research_agent[0m
2025-07-30 15:42:42 - [32mINFO[0m - Loading saved prompt: summary[0m
2025-07-30 15:42:42 - [32mINFO[0m - Loading saved prompt: vision_pdf[0m
2025-07-30 15:42:42 - [32mINFO[0m - Loading saved prompt: graph_extraction[0m
2025-07-30 15:42:42 - [32mINFO[0m - Loading saved prompt: hyde[0m
2025-07-30 15:42:42 - [32mINFO[0m - Loading saved prompt: rag[0m
2025-07-30 15:42:42 - [32mINFO[0m - Loading saved prompt: static_rag_agent[0m
2025-07-30 15:42:42 - [32mINFO[0m - Loading saved prompt: system[0m
2025-07-30 15:42:42 - [32mINFO[0m - Loading saved prompt: vision_img[0m
2025-07-30 15:42:42 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent[0m
2025-07-30 15:42:42 - [32mINFO[0m - Loading saved prompt: graph_communities[0m
2025-07-30 15:42:42 - [32mINFO[0m - Loading saved prompt: chunk_enrichment[0m
2025-07-30 15:42:42 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent_xml_tooling[0m
2025-07-30 15:42:42 - [32mINFO[0m - Loading saved prompt: graph_entity_description[0m
2025-07-30 15:42:42 - [32mINFO[0m - Loading prompts from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts[0m
2025-07-30 15:42:42 - [32mINFO[0m - Loading default prompt: chunk_enrichment from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\chunk_enrichment.yaml.[0m
2025-07-30 15:42:42 - [32mINFO[0m - Loading default prompt: collection_summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\collection_summary.yaml.[0m
2025-07-30 15:42:42 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\dynamic_rag_agent.yaml.[0m
2025-07-30 15:42:42 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent_xml_tooling from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\dynamic_rag_agent_xml_tooling.yaml.[0m
2025-07-30 15:42:42 - [32mINFO[0m - Loading default prompt: graph_communities from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_communities.yaml.[0m
2025-07-30 15:42:42 - [32mINFO[0m - Loading default prompt: graph_entity_description from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_entity_description.yaml.[0m
2025-07-30 15:42:43 - [32mINFO[0m - Loading default prompt: graph_extraction from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_extraction.yaml.[0m
2025-07-30 15:42:43 - [32mINFO[0m - Loading default prompt: hyde from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\hyde.yaml.[0m
2025-07-30 15:42:43 - [32mINFO[0m - Loading default prompt: rag from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\rag.yaml.[0m
2025-07-30 15:42:43 - [32mINFO[0m - Loading default prompt: rag_fusion from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\rag_fusion.yaml.[0m
2025-07-30 15:42:43 - [32mINFO[0m - Loading default prompt: static_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\static_rag_agent.yaml.[0m
2025-07-30 15:42:43 - [32mINFO[0m - Loading default prompt: static_research_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\static_research_agent.yaml.[0m
2025-07-30 15:42:44 - [32mINFO[0m - Loading default prompt: summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\summary.yaml.[0m
2025-07-30 15:42:44 - [32mINFO[0m - Loading default prompt: system from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\system.yaml.[0m
2025-07-30 15:42:44 - [32mINFO[0m - Loading default prompt: vision_img from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\vision_img.yaml.[0m
2025-07-30 15:42:44 - [32mINFO[0m - Loading default prompt: vision_pdf from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\vision_pdf.yaml.[0m
2025-07-30 15:42:45 - [33mWARNING[0m - Dimension mismatch detected: Table 'cscsrag.graphs_entities' description_embedding has dimension 512, but 1024 is required. Recreating table with new dimension...[0m
2025-07-30 15:42:45 - [32mINFO[0m - Dropped existing table cscsrag.graphs_entities[0m
2025-07-30 15:42:45 - [33mWARNING[0m - Dimension mismatch detected: Table 'cscsrag.documents_entities' description_embedding has dimension 512, but 1024 is required. Recreating table with new dimension...[0m
2025-07-30 15:42:45 - [32mINFO[0m - Dropped existing table cscsrag.documents_entities[0m
2025-07-30 15:42:50 - [32mINFO[0m - Using existing S3 bucket: rag[0m
2025-07-30 15:42:50 - [32mINFO[0m - Initializing OCRProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='mistral' model='mistral-ocr-latest' concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0[0m
2025-07-30 15:42:50 - [33mWARNING[0m - MISTRAL_API_KEY not set in environment, if you plan to use Mistral OCR, please set it.[0m
2025-07-30 15:42:56 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 15:42:56 - [32mINFO[0m - Initialized extra parser zerox for pdf[0m
2025-07-30 15:42:56 - [32mINFO[0m - Initialized extra parser ocr for pdf[0m
2025-07-30 15:42:56 - [32mINFO[0m - R2RIngestionProvider initialized with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' excluded_parsers=[] chunking_strategy=<ChunkingStrategy.RECURSIVE: 'recursive'> chunk_size=1024 chunk_overlap=512 chunk_enrichment_settings=ChunkEnrichmentSettings(enable_chunk_enrichment=False, n_chunks=2, generation_config=None, chunk_enrichment_prompt='chunk_enrichment') extra_parsers={'pdf': ['zerox', 'ocr']} audio_transcription_model=None vlm=None vlm_batch_size=20 vlm_max_tokens_to_sample=1024 max_concurrent_vlm_tasks=20 vlm_ocr_one_page_per_chunk=True skip_document_summary=False document_summary_system_prompt='system' document_summary_task_prompt='summary' chunks_for_document_summary=128 document_summary_model=None parser_overrides={} automatic_extraction=True document_summary_max_length=100000 separator=None[0m
2025-07-30 15:42:57 - [32mINFO[0m - Default admin user already exists.[0m
2025-07-30 15:42:58 - [31mERROR[0m - Error 6 validation errors for R2RProviders
embedding.is-instance[LiteLLMEmbeddingProvider]
  Input should be an instance of LiteLLMEmbeddingProvider [type=is_instance_of, input_value=<core.providers.embedding...t at 0x0000025D2AC87D10>, input_type=NewAPIEmbeddingProvider]
    For further information visit https://errors.pydantic.dev/2.11/v/is_instance_of
embedding.is-instance[OpenAIEmbeddingProvider]
  Input should be an instance of OpenAIEmbeddingProvider [type=is_instance_of, input_value=<core.providers.embedding...t at 0x0000025D2AC87D10>, input_type=NewAPIEmbeddingProvider]
    For further information visit https://errors.pydantic.dev/2.11/v/is_instance_of
embedding.is-instance[OllamaEmbeddingProvider]
  Input should be an instance of OllamaEmbeddingProvider [type=is_instance_of, input_value=<core.providers.embedding...t at 0x0000025D2AC87D10>, input_type=NewAPIEmbeddingProvider]
    For further information visit https://errors.pydantic.dev/2.11/v/is_instance_of
completion_embedding.is-instance[LiteLLMEmbeddingProvider]
  Input should be an instance of LiteLLMEmbeddingProvider [type=is_instance_of, input_value=<core.providers.embedding...t at 0x0000025D2B025AD0>, input_type=NewAPIEmbeddingProvider]
    For further information visit https://errors.pydantic.dev/2.11/v/is_instance_of
completion_embedding.is-instance[OpenAIEmbeddingProvider]
  Input should be an instance of OpenAIEmbeddingProvider [type=is_instance_of, input_value=<core.providers.embedding...t at 0x0000025D2B025AD0>, input_type=NewAPIEmbeddingProvider]
    For further information visit https://errors.pydantic.dev/2.11/v/is_instance_of
completion_embedding.is-instance[OllamaEmbeddingProvider]
  Input should be an instance of OllamaEmbeddingProvider [type=is_instance_of, input_value=<core.providers.embedding...t at 0x0000025D2B025AD0>, input_type=NewAPIEmbeddingProvider]
    For further information visit https://errors.pydantic.dev/2.11/v/is_instance_of while creating R2RProviders.[0m
2025-07-30 15:42:58 - [31mERROR[0m - Failed to start R2R server: 6 validation errors for R2RProviders
embedding.is-instance[LiteLLMEmbeddingProvider]
  Input should be an instance of LiteLLMEmbeddingProvider [type=is_instance_of, input_value=<core.providers.embedding...t at 0x0000025D2AC87D10>, input_type=NewAPIEmbeddingProvider]
    For further information visit https://errors.pydantic.dev/2.11/v/is_instance_of
embedding.is-instance[OpenAIEmbeddingProvider]
  Input should be an instance of OpenAIEmbeddingProvider [type=is_instance_of, input_value=<core.providers.embedding...t at 0x0000025D2AC87D10>, input_type=NewAPIEmbeddingProvider]
    For further information visit https://errors.pydantic.dev/2.11/v/is_instance_of
embedding.is-instance[OllamaEmbeddingProvider]
  Input should be an instance of OllamaEmbeddingProvider [type=is_instance_of, input_value=<core.providers.embedding...t at 0x0000025D2AC87D10>, input_type=NewAPIEmbeddingProvider]
    For further information visit https://errors.pydantic.dev/2.11/v/is_instance_of
completion_embedding.is-instance[LiteLLMEmbeddingProvider]
  Input should be an instance of LiteLLMEmbeddingProvider [type=is_instance_of, input_value=<core.providers.embedding...t at 0x0000025D2B025AD0>, input_type=NewAPIEmbeddingProvider]
    For further information visit https://errors.pydantic.dev/2.11/v/is_instance_of
completion_embedding.is-instance[OpenAIEmbeddingProvider]
  Input should be an instance of OpenAIEmbeddingProvider [type=is_instance_of, input_value=<core.providers.embedding...t at 0x0000025D2B025AD0>, input_type=NewAPIEmbeddingProvider]
    For further information visit https://errors.pydantic.dev/2.11/v/is_instance_of
completion_embedding.is-instance[OllamaEmbeddingProvider]
  Input should be an instance of OllamaEmbeddingProvider [type=is_instance_of, input_value=<core.providers.embedding...t at 0x0000025D2B025AD0>, input_type=NewAPIEmbeddingProvider]
    For further information visit https://errors.pydantic.dev/2.11/v/is_instance_of[0m
2025-07-30 15:51:27 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 15:51:27 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://***********:3000/v1'} provider='newapi' base_model='aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 15:51:27 - [32mINFO[0m - Initialized NewAPIEmbeddingProvider with model: aliyun/text-embedding-v4[0m
2025-07-30 15:51:27 - [32mINFO[0m - API Base: http://***********:3000/v1[0m
2025-07-30 15:51:27 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://***********:3000/v1'} provider='newapi' base_model='aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 15:51:27 - [32mINFO[0m - Initialized NewAPIEmbeddingProvider with model: aliyun/text-embedding-v4[0m
2025-07-30 15:51:27 - [32mINFO[0m - API Base: http://***********:3000/v1[0m
2025-07-30 15:51:27 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:51:27 - [32mINFO[0m - Initializing R2RCompletionProvider...[0m
2025-07-30 15:51:27 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:51:39 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:51:42 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:51:42 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 15:51:42 - [32mINFO[0m - Initializing BcryptCryptoProvider[0m
2025-07-30 15:51:42 - [32mINFO[0m - Initializing DatabaseProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='postgres' user=None password=None host=None port=None db_name=None project_name=None postgres_configuration_settings=None default_collection_name='Default' default_collection_description='Your default collection.' collection_summary_system_prompt='system' collection_summary_prompt='collection_summary' disable_create_extension=False batch_size=1 graph_search_results_store_path=None graph_enrichment_settings=GraphEnrichmentSettings(force_graph_search_results_enrichment=False, graph_communities_prompt='graph_communities', max_summary_input_length=65536, generation_config=None, leiden_params={}) graph_creation_settings=GraphCreationSettings(graph_extraction_prompt='graph_extraction', graph_entity_description_prompt='graph_entity_description', entity_types=[], relation_types=[], chunk_merge_count=2, max_knowledge_relationships=100, max_description_input_length=65536, generation_config=None, automatic_deduplication=True) graph_search_settings=GraphSearchSettings(limits={}, enabled=True) limits=LimitSettings(global_per_min=60, route_per_min=20, monthly_limit=10000) maintenance=MaintenanceSettings(vacuum_schedule='0 3 * * *', vacuum_analyze=True, vacuum_full=False) route_limits={} user_limits={}.[0m
2025-07-30 15:51:42 - [32mINFO[0m - Connecting to Postgres via TCP/IP[0m
2025-07-30 15:51:42 - [32mINFO[0m - Initializing `PostgresDatabaseProvider`.[0m
2025-07-30 15:51:42 - [32mINFO[0m - Connecting with 230 connections to `asyncpg.create_pool`.[0m
2025-07-30 15:51:43 - [32mINFO[0m - Successfully connected to Postgres database and created connection pool.[0m
2025-07-30 15:51:43 - [32mINFO[0m - Creating table, if it does not exist: "cscsrag"."documents"[0m
2025-07-30 15:51:45 - [32mINFO[0m - Loading saved prompt: collection_summary[0m
2025-07-30 15:51:45 - [32mINFO[0m - Loading saved prompt: static_research_agent[0m
2025-07-30 15:51:45 - [32mINFO[0m - Loading saved prompt: vision_pdf[0m
2025-07-30 15:51:45 - [32mINFO[0m - Loading saved prompt: graph_extraction[0m
2025-07-30 15:51:45 - [32mINFO[0m - Loading saved prompt: hyde[0m
2025-07-30 15:51:45 - [32mINFO[0m - Loading saved prompt: rag_fusion[0m
2025-07-30 15:51:45 - [32mINFO[0m - Loading saved prompt: static_rag_agent[0m
2025-07-30 15:51:45 - [32mINFO[0m - Loading saved prompt: summary[0m
2025-07-30 15:51:45 - [32mINFO[0m - Loading saved prompt: system[0m
2025-07-30 15:51:45 - [32mINFO[0m - Loading saved prompt: rag[0m
2025-07-30 15:51:45 - [32mINFO[0m - Loading saved prompt: vision_img[0m
2025-07-30 15:51:45 - [32mINFO[0m - Loading saved prompt: graph_communities[0m
2025-07-30 15:51:45 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent[0m
2025-07-30 15:51:45 - [32mINFO[0m - Loading saved prompt: chunk_enrichment[0m
2025-07-30 15:51:45 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent_xml_tooling[0m
2025-07-30 15:51:45 - [32mINFO[0m - Loading saved prompt: graph_entity_description[0m
2025-07-30 15:51:45 - [32mINFO[0m - Loading prompts from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts[0m
2025-07-30 15:51:45 - [32mINFO[0m - Loading default prompt: chunk_enrichment from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\chunk_enrichment.yaml.[0m
2025-07-30 15:51:45 - [32mINFO[0m - Loading default prompt: collection_summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\collection_summary.yaml.[0m
2025-07-30 15:51:46 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\dynamic_rag_agent.yaml.[0m
2025-07-30 15:51:46 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent_xml_tooling from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\dynamic_rag_agent_xml_tooling.yaml.[0m
2025-07-30 15:51:46 - [32mINFO[0m - Loading default prompt: graph_communities from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_communities.yaml.[0m
2025-07-30 15:51:46 - [32mINFO[0m - Loading default prompt: graph_entity_description from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_entity_description.yaml.[0m
2025-07-30 15:51:46 - [32mINFO[0m - Loading default prompt: graph_extraction from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_extraction.yaml.[0m
2025-07-30 15:51:46 - [32mINFO[0m - Loading default prompt: hyde from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\hyde.yaml.[0m
2025-07-30 15:51:46 - [32mINFO[0m - Loading default prompt: rag from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\rag.yaml.[0m
2025-07-30 15:51:47 - [32mINFO[0m - Loading default prompt: rag_fusion from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\rag_fusion.yaml.[0m
2025-07-30 15:51:47 - [32mINFO[0m - Loading default prompt: static_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\static_rag_agent.yaml.[0m
2025-07-30 15:51:47 - [32mINFO[0m - Loading default prompt: static_research_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\static_research_agent.yaml.[0m
2025-07-30 15:51:47 - [32mINFO[0m - Loading default prompt: summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\summary.yaml.[0m
2025-07-30 15:51:47 - [32mINFO[0m - Loading default prompt: system from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\system.yaml.[0m
2025-07-30 15:51:47 - [32mINFO[0m - Loading default prompt: vision_img from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\vision_img.yaml.[0m
2025-07-30 15:51:47 - [32mINFO[0m - Loading default prompt: vision_pdf from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\vision_pdf.yaml.[0m
2025-07-30 15:51:51 - [32mINFO[0m - Using existing S3 bucket: rag[0m
2025-07-30 15:51:51 - [32mINFO[0m - Initializing OCRProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='mistral' model='mistral-ocr-latest' concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0[0m
2025-07-30 15:51:51 - [33mWARNING[0m - MISTRAL_API_KEY not set in environment, if you plan to use Mistral OCR, please set it.[0m
2025-07-30 15:51:54 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 15:51:54 - [32mINFO[0m - Initialized extra parser zerox for pdf[0m
2025-07-30 15:51:54 - [32mINFO[0m - Initialized extra parser ocr for pdf[0m
2025-07-30 15:51:54 - [32mINFO[0m - R2RIngestionProvider initialized with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' excluded_parsers=[] chunking_strategy=<ChunkingStrategy.RECURSIVE: 'recursive'> chunk_size=1024 chunk_overlap=512 chunk_enrichment_settings=ChunkEnrichmentSettings(enable_chunk_enrichment=False, n_chunks=2, generation_config=None, chunk_enrichment_prompt='chunk_enrichment') extra_parsers={'pdf': ['zerox', 'ocr']} audio_transcription_model=None vlm=None vlm_batch_size=20 vlm_max_tokens_to_sample=1024 max_concurrent_vlm_tasks=20 vlm_ocr_one_page_per_chunk=True skip_document_summary=False document_summary_system_prompt='system' document_summary_task_prompt='summary' chunks_for_document_summary=128 document_summary_model=None parser_overrides={} automatic_extraction=True document_summary_max_length=100000 separator=None[0m
2025-07-30 15:51:54 - [32mINFO[0m - Default admin user already exists.[0m
2025-07-30 15:51:54 - [32mINFO[0m - Initializing database maintenance service[0m
2025-07-30 15:51:54 - [32mINFO[0m - Scheduler started[0m
2025-07-30 15:51:54 - [32mINFO[0m - Scheduler started[0m
2025-07-30 15:51:54 - [32mINFO[0m - Adding job vacuum_database with trigger cron and kwargs {'minute': '0', 'hour': '3', 'day': '*', 'month': '*', 'day_of_week': '*', 'kwargs': {'full': False, 'analyze': True}}[0m
2025-07-30 15:51:54 - [32mINFO[0m - Added job "MaintenanceService.vacuum_database" to job store "default"[0m
2025-07-30 15:51:54 - [32mINFO[0m - Initializing ChunksRouter[0m
2025-07-30 15:51:54 - [32mINFO[0m - Initializing CollectionsRouter[0m
2025-07-30 15:51:55 - [32mINFO[0m - Initializing ConversationsRouter[0m
2025-07-30 15:51:55 - [32mINFO[0m - Initializing DocumentsRouter[0m
2025-07-30 15:51:55 - [32mINFO[0m - Initializing GraphRouter[0m
2025-07-30 15:51:55 - [32mINFO[0m - Initializing IndicesRouter[0m
2025-07-30 15:51:55 - [32mINFO[0m - Initializing PromptsRouter[0m
2025-07-30 15:51:55 - [32mINFO[0m - Initializing RetrievalRouter[0m
2025-07-30 15:51:56 - [32mINFO[0m - Initializing SystemRouter[0m
2025-07-30 15:51:56 - [32mINFO[0m - Initializing UsersRouter[0m
2025-07-30 15:51:57 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 15:51:58 - [32mINFO[0m - Started server process [8124][0m
2025-07-30 15:51:58 - [32mINFO[0m - Waiting for application startup.[0m
2025-07-30 15:51:58 - [32mINFO[0m - Application startup complete.[0m
2025-07-30 15:51:58 - [32mINFO[0m - Uvicorn running on http://0.0.0.0:7272 (Press CTRL+C to quit)[0m
2025-07-30 15:52:57 - [32mINFO[0m - 127.0.0.1:59240 - "POST /v3/users/login HTTP/1.1" [32m200[0m
2025-07-30 15:52:57 - [32mINFO[0m - 127.0.0.1:59240 - "OPTIONS /v3/users/me HTTP/1.1" [32m200[0m
2025-07-30 15:52:58 - [32mINFO[0m - 127.0.0.1:59240 - "GET /v3/users/me HTTP/1.1" [32m200[0m
2025-07-30 15:52:58 - [32mINFO[0m - 127.0.0.1:59240 - "OPTIONS /v3/system/settings HTTP/1.1" [32m200[0m
2025-07-30 15:52:58 - [32mINFO[0m - 127.0.0.1:59240 - "GET /v3/system/settings HTTP/1.1" [32m200[0m
2025-07-30 15:52:59 - [32mINFO[0m - 127.0.0.1:59241 - "OPTIONS /v3/system/status HTTP/1.1" [32m200[0m
2025-07-30 15:52:59 - [32mINFO[0m - 127.0.0.1:59240 - "OPTIONS /v3/system/status HTTP/1.1" [32m200[0m
2025-07-30 15:53:00 - [32mINFO[0m - 127.0.0.1:59241 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-07-30 15:53:01 - [32mINFO[0m - 127.0.0.1:59240 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-07-30 15:53:02 - [32mINFO[0m - 127.0.0.1:59240 - "OPTIONS /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-07-30 15:53:03 - [32mINFO[0m - 127.0.0.1:59240 - "GET /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-07-30 15:53:33 - [32mINFO[0m - 127.0.0.1:59301 - "OPTIONS /v3/documents HTTP/1.1" [32m200[0m
2025-07-30 15:53:33 - [33mWARNING[0m - Skipping data after last boundary[0m
2025-07-30 15:53:34 - [32mINFO[0m - 127.0.0.1:59302 - "GET /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-07-30 15:53:36 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 15:53:36 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 15:53:36 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 15:53:51 - [32mINFO[0m - NewAPI embedding request kwargs: {'model': 'aliyun/text-embedding-v4', 'api_base': 'http://***********:3000/v1', 'api_key': 'sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI'}[0m
2025-07-30 15:53:52 - [31mERROR[0m - Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 15:53:52 - [33mWARNING[0m - Request failed (attempt 1): Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 15:53:52 - [32mINFO[0m - NewAPI embedding request kwargs: {'model': 'aliyun/text-embedding-v4', 'api_base': 'http://***********:3000/v1', 'api_key': 'sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI'}[0m
2025-07-30 15:53:52 - [31mERROR[0m - Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 15:53:52 - [33mWARNING[0m - Request failed (attempt 2): Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 15:53:53 - [32mINFO[0m - NewAPI embedding request kwargs: {'model': 'aliyun/text-embedding-v4', 'api_base': 'http://***********:3000/v1', 'api_key': 'sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI'}[0m
2025-07-30 15:53:53 - [31mERROR[0m - Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 15:53:53 - [33mWARNING[0m - Request failed (attempt 3): Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 15:53:53 - [31mERROR[0m - Error running orchestrated ingestion: Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers 

Attempting to run without orchestration.[0m
2025-07-30 15:53:53 - [32mINFO[0m - Running ingestion without orchestration for file ��ְ�����ݽ���.pdf and document_id 55b09cdc-5de0-567a-bf36-dcdd237269fa.[0m
2025-07-30 15:53:54 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 15:53:54 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 15:53:54 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 15:54:13 - [32mINFO[0m - NewAPI embedding request kwargs: {'model': 'aliyun/text-embedding-v4', 'api_base': 'http://***********:3000/v1', 'api_key': 'sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI'}[0m
2025-07-30 15:54:13 - [31mERROR[0m - Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 15:54:13 - [33mWARNING[0m - Request failed (attempt 1): Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 15:54:13 - [32mINFO[0m - NewAPI embedding request kwargs: {'model': 'aliyun/text-embedding-v4', 'api_base': 'http://***********:3000/v1', 'api_key': 'sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI'}[0m
2025-07-30 15:54:13 - [31mERROR[0m - Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 15:54:13 - [33mWARNING[0m - Request failed (attempt 2): Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 15:54:13 - [32mINFO[0m - NewAPI embedding request kwargs: {'model': 'aliyun/text-embedding-v4', 'api_base': 'http://***********:3000/v1', 'api_key': 'sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI'}[0m
2025-07-30 15:54:13 - [31mERROR[0m - Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 15:54:13 - [33mWARNING[0m - Request failed (attempt 3): Error getting embeddings: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=aliyun/text-embedding-v4
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers[0m
2025-07-30 15:54:14 - [32mINFO[0m - 127.0.0.1:59303 - "GET /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-07-30 15:58:55 - [32mINFO[0m - Shutting down[0m
2025-07-30 15:58:55 - [32mINFO[0m - Waiting for application shutdown.[0m
2025-07-30 15:58:55 - [32mINFO[0m - Application shutdown complete.[0m
2025-07-30 15:58:55 - [32mINFO[0m - Finished server process [8124][0m
2025-07-30 16:00:11 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 16:00:11 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://***********:3000/v1'} provider='newapi' base_model='aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 16:00:11 - [32mINFO[0m - Configured custom model mapping: aliyun/text-embedding-v4 -> openai compatible[0m
2025-07-30 16:00:11 - [32mINFO[0m - Initialized NewAPIEmbeddingProvider with model: aliyun/text-embedding-v4[0m
2025-07-30 16:00:11 - [32mINFO[0m - API Base: http://***********:3000/v1[0m
2025-07-30 16:00:11 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://***********:3000/v1'} provider='newapi' base_model='aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 16:00:11 - [32mINFO[0m - Configured custom model mapping: aliyun/text-embedding-v4 -> openai compatible[0m
2025-07-30 16:00:11 - [32mINFO[0m - Initialized NewAPIEmbeddingProvider with model: aliyun/text-embedding-v4[0m
2025-07-30 16:00:11 - [32mINFO[0m - API Base: http://***********:3000/v1[0m
2025-07-30 16:00:11 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 16:00:11 - [32mINFO[0m - Initializing R2RCompletionProvider...[0m
2025-07-30 16:00:11 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 16:00:25 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 16:00:29 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 16:00:29 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 16:00:29 - [32mINFO[0m - Initializing BcryptCryptoProvider[0m
2025-07-30 16:00:29 - [32mINFO[0m - Initializing DatabaseProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='postgres' user=None password=None host=None port=None db_name=None project_name=None postgres_configuration_settings=None default_collection_name='Default' default_collection_description='Your default collection.' collection_summary_system_prompt='system' collection_summary_prompt='collection_summary' disable_create_extension=False batch_size=1 graph_search_results_store_path=None graph_enrichment_settings=GraphEnrichmentSettings(force_graph_search_results_enrichment=False, graph_communities_prompt='graph_communities', max_summary_input_length=65536, generation_config=None, leiden_params={}) graph_creation_settings=GraphCreationSettings(graph_extraction_prompt='graph_extraction', graph_entity_description_prompt='graph_entity_description', entity_types=[], relation_types=[], chunk_merge_count=2, max_knowledge_relationships=100, max_description_input_length=65536, generation_config=None, automatic_deduplication=True) graph_search_settings=GraphSearchSettings(limits={}, enabled=True) limits=LimitSettings(global_per_min=60, route_per_min=20, monthly_limit=10000) maintenance=MaintenanceSettings(vacuum_schedule='0 3 * * *', vacuum_analyze=True, vacuum_full=False) route_limits={} user_limits={}.[0m
2025-07-30 16:00:29 - [32mINFO[0m - Connecting to Postgres via TCP/IP[0m
2025-07-30 16:00:29 - [32mINFO[0m - Initializing `PostgresDatabaseProvider`.[0m
2025-07-30 16:00:29 - [32mINFO[0m - Connecting with 230 connections to `asyncpg.create_pool`.[0m
2025-07-30 16:00:36 - [32mINFO[0m - Successfully connected to Postgres database and created connection pool.[0m
2025-07-30 16:00:36 - [32mINFO[0m - Creating table, if it does not exist: "cscsrag"."documents"[0m
2025-07-30 16:00:42 - [32mINFO[0m - Loading saved prompt: collection_summary[0m
2025-07-30 16:00:42 - [32mINFO[0m - Loading saved prompt: vision_pdf[0m
2025-07-30 16:00:42 - [32mINFO[0m - Loading saved prompt: graph_extraction[0m
2025-07-30 16:00:42 - [32mINFO[0m - Loading saved prompt: hyde[0m
2025-07-30 16:00:42 - [32mINFO[0m - Loading saved prompt: rag_fusion[0m
2025-07-30 16:00:42 - [32mINFO[0m - Loading saved prompt: static_rag_agent[0m
2025-07-30 16:00:42 - [32mINFO[0m - Loading saved prompt: static_research_agent[0m
2025-07-30 16:00:42 - [32mINFO[0m - Loading saved prompt: summary[0m
2025-07-30 16:00:42 - [32mINFO[0m - Loading saved prompt: rag[0m
2025-07-30 16:00:42 - [32mINFO[0m - Loading saved prompt: system[0m
2025-07-30 16:00:42 - [32mINFO[0m - Loading saved prompt: vision_img[0m
2025-07-30 16:00:42 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent[0m
2025-07-30 16:00:42 - [32mINFO[0m - Loading saved prompt: chunk_enrichment[0m
2025-07-30 16:00:42 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent_xml_tooling[0m
2025-07-30 16:00:42 - [32mINFO[0m - Loading saved prompt: graph_communities[0m
2025-07-30 16:00:42 - [32mINFO[0m - Loading saved prompt: graph_entity_description[0m
2025-07-30 16:00:42 - [32mINFO[0m - Loading prompts from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts[0m
2025-07-30 16:00:42 - [32mINFO[0m - Loading default prompt: chunk_enrichment from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\chunk_enrichment.yaml.[0m
2025-07-30 16:00:42 - [32mINFO[0m - Loading default prompt: collection_summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\collection_summary.yaml.[0m
2025-07-30 16:00:42 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\dynamic_rag_agent.yaml.[0m
2025-07-30 16:00:42 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent_xml_tooling from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\dynamic_rag_agent_xml_tooling.yaml.[0m
2025-07-30 16:00:42 - [32mINFO[0m - Loading default prompt: graph_communities from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_communities.yaml.[0m
2025-07-30 16:00:43 - [32mINFO[0m - Loading default prompt: graph_entity_description from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_entity_description.yaml.[0m
2025-07-30 16:00:43 - [32mINFO[0m - Loading default prompt: graph_extraction from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_extraction.yaml.[0m
2025-07-30 16:00:43 - [32mINFO[0m - Loading default prompt: hyde from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\hyde.yaml.[0m
2025-07-30 16:00:44 - [32mINFO[0m - Loading default prompt: rag from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\rag.yaml.[0m
2025-07-30 16:00:44 - [32mINFO[0m - Loading default prompt: rag_fusion from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\rag_fusion.yaml.[0m
2025-07-30 16:00:44 - [32mINFO[0m - Loading default prompt: static_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\static_rag_agent.yaml.[0m
2025-07-30 16:00:44 - [32mINFO[0m - Loading default prompt: static_research_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\static_research_agent.yaml.[0m
2025-07-30 16:00:44 - [32mINFO[0m - Loading default prompt: summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\summary.yaml.[0m
2025-07-30 16:00:44 - [32mINFO[0m - Loading default prompt: system from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\system.yaml.[0m
2025-07-30 16:00:45 - [32mINFO[0m - Loading default prompt: vision_img from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\vision_img.yaml.[0m
2025-07-30 16:00:45 - [32mINFO[0m - Loading default prompt: vision_pdf from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\vision_pdf.yaml.[0m
2025-07-30 16:00:53 - [32mINFO[0m - Using existing S3 bucket: rag[0m
2025-07-30 16:00:53 - [32mINFO[0m - Initializing OCRProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='mistral' model='mistral-ocr-latest' concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0[0m
2025-07-30 16:00:53 - [33mWARNING[0m - MISTRAL_API_KEY not set in environment, if you plan to use Mistral OCR, please set it.[0m
2025-07-30 16:00:56 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:00:56 - [32mINFO[0m - Initialized extra parser zerox for pdf[0m
2025-07-30 16:00:56 - [32mINFO[0m - Initialized extra parser ocr for pdf[0m
2025-07-30 16:00:56 - [32mINFO[0m - R2RIngestionProvider initialized with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' excluded_parsers=[] chunking_strategy=<ChunkingStrategy.RECURSIVE: 'recursive'> chunk_size=1024 chunk_overlap=512 chunk_enrichment_settings=ChunkEnrichmentSettings(enable_chunk_enrichment=False, n_chunks=2, generation_config=None, chunk_enrichment_prompt='chunk_enrichment') extra_parsers={'pdf': ['zerox', 'ocr']} audio_transcription_model=None vlm=None vlm_batch_size=20 vlm_max_tokens_to_sample=1024 max_concurrent_vlm_tasks=20 vlm_ocr_one_page_per_chunk=True skip_document_summary=False document_summary_system_prompt='system' document_summary_task_prompt='summary' chunks_for_document_summary=128 document_summary_model=None parser_overrides={} automatic_extraction=True document_summary_max_length=100000 separator=None[0m
2025-07-30 16:00:57 - [32mINFO[0m - Default admin user already exists.[0m
2025-07-30 16:00:57 - [32mINFO[0m - Initializing database maintenance service[0m
2025-07-30 16:00:57 - [32mINFO[0m - Scheduler started[0m
2025-07-30 16:00:57 - [32mINFO[0m - Scheduler started[0m
2025-07-30 16:00:57 - [32mINFO[0m - Adding job vacuum_database with trigger cron and kwargs {'minute': '0', 'hour': '3', 'day': '*', 'month': '*', 'day_of_week': '*', 'kwargs': {'full': False, 'analyze': True}}[0m
2025-07-30 16:00:57 - [32mINFO[0m - Added job "MaintenanceService.vacuum_database" to job store "default"[0m
2025-07-30 16:00:57 - [32mINFO[0m - Initializing ChunksRouter[0m
2025-07-30 16:00:57 - [32mINFO[0m - Initializing CollectionsRouter[0m
2025-07-30 16:00:57 - [32mINFO[0m - Initializing ConversationsRouter[0m
2025-07-30 16:00:57 - [32mINFO[0m - Initializing DocumentsRouter[0m
2025-07-30 16:00:57 - [32mINFO[0m - Initializing GraphRouter[0m
2025-07-30 16:00:57 - [32mINFO[0m - Initializing IndicesRouter[0m
2025-07-30 16:00:57 - [32mINFO[0m - Initializing PromptsRouter[0m
2025-07-30 16:00:57 - [32mINFO[0m - Initializing RetrievalRouter[0m
2025-07-30 16:00:57 - [32mINFO[0m - Initializing SystemRouter[0m
2025-07-30 16:00:57 - [32mINFO[0m - Initializing UsersRouter[0m
2025-07-30 16:00:58 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 16:00:58 - [32mINFO[0m - Started server process [16432][0m
2025-07-30 16:00:58 - [32mINFO[0m - Waiting for application startup.[0m
2025-07-30 16:00:59 - [32mINFO[0m - Application startup complete.[0m
2025-07-30 16:00:59 - [32mINFO[0m - Uvicorn running on http://0.0.0.0:7272 (Press CTRL+C to quit)[0m
2025-07-30 16:02:35 - [32mINFO[0m - 127.0.0.1:60169 - "POST /v3/users/login HTTP/1.1" [32m200[0m
2025-07-30 16:02:35 - [32mINFO[0m - 127.0.0.1:60169 - "GET /v3/users/me HTTP/1.1" [32m200[0m
2025-07-30 16:02:36 - [32mINFO[0m - 127.0.0.1:60169 - "GET /v3/system/settings HTTP/1.1" [32m200[0m
2025-07-30 16:02:37 - [32mINFO[0m - 127.0.0.1:60169 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-07-30 16:02:38 - [32mINFO[0m - 127.0.0.1:60171 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-07-30 16:02:41 - [32mINFO[0m - 127.0.0.1:60171 - "GET /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-07-30 16:02:47 - [32mINFO[0m - 127.0.0.1:60202 - "OPTIONS /v3/documents/55b09cdc-5de0-567a-bf36-dcdd237269fa HTTP/1.1" [32m200[0m
2025-07-30 16:02:50 - [32mINFO[0m - 127.0.0.1:60202 - "DELETE /v3/documents/55b09cdc-5de0-567a-bf36-dcdd237269fa HTTP/1.1" [32m200[0m
2025-07-30 16:03:44 - [33mWARNING[0m - 127.0.0.1:60203 - "DELETE /v3/documents/55b09cdc-5de0-567a-bf36-dcdd237269fa HTTP/1.1" [33m404[0m
2025-07-30 16:04:20 - [32mINFO[0m - 127.0.0.1:60338 - "OPTIONS /v3/documents HTTP/1.1" [32m200[0m
2025-07-30 16:04:20 - [33mWARNING[0m - Skipping data after last boundary[0m
2025-07-30 16:04:23 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:04:23 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:04:23 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:04:36 - [32mINFO[0m - Converted model name from aliyun/text-embedding-v4 to openai/text-embedding-v4 for LiteLLM compatibility[0m
2025-07-30 16:04:36 - [32mINFO[0m - NewAPI embedding request kwargs: {'model': 'openai/text-embedding-v4', 'api_base': 'http://***********:3000/v1', 'api_key': 'sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI'}[0m
2025-07-30 16:04:38 - [32mINFO[0m - Retrying request to /embeddings in 0.495042 seconds[0m
2025-07-30 16:04:38 - [32mINFO[0m - Retrying request to /embeddings in 0.865773 seconds[0m
2025-07-30 16:04:40 - [31mERROR[0m - Error getting embeddings: litellm.ServiceUnavailableError: ServiceUnavailableError: OpenAIException - Error code: 503 - {'error': {'message': '��ǰ���� �Ƽ��з����� �¶���ģ�� text-embedding-v4 �޿������� (request id: 202507301604381084712275to7b1Cb)', 'type': 'new_api_error'}}[0m
2025-07-30 16:04:40 - [33mWARNING[0m - Request failed (attempt 1): Error getting embeddings: litellm.ServiceUnavailableError: ServiceUnavailableError: OpenAIException - Error code: 503 - {'error': {'message': '��ǰ���� �Ƽ��з����� �¶���ģ�� text-embedding-v4 �޿������� (request id: 202507301604381084712275to7b1Cb)', 'type': 'new_api_error'}}[0m
2025-07-30 16:04:40 - [32mINFO[0m - Converted model name from aliyun/text-embedding-v4 to openai/text-embedding-v4 for LiteLLM compatibility[0m
2025-07-30 16:04:40 - [32mINFO[0m - NewAPI embedding request kwargs: {'model': 'openai/text-embedding-v4', 'api_base': 'http://***********:3000/v1', 'api_key': 'sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI'}[0m
2025-07-30 16:04:40 - [32mINFO[0m - Retrying request to /embeddings in 0.422838 seconds[0m
2025-07-30 16:04:41 - [32mINFO[0m - Retrying request to /embeddings in 0.884696 seconds[0m
2025-07-30 16:04:42 - [31mERROR[0m - Error getting embeddings: litellm.ServiceUnavailableError: ServiceUnavailableError: OpenAIException - Error code: 503 - {'error': {'message': '��ǰ���� �Ƽ��з����� �¶���ģ�� text-embedding-v4 �޿������� (request id: 20250730160440104041107CSfKKBin)', 'type': 'new_api_error'}}[0m
2025-07-30 16:04:42 - [33mWARNING[0m - Request failed (attempt 2): Error getting embeddings: litellm.ServiceUnavailableError: ServiceUnavailableError: OpenAIException - Error code: 503 - {'error': {'message': '��ǰ���� �Ƽ��з����� �¶���ģ�� text-embedding-v4 �޿������� (request id: 20250730160440104041107CSfKKBin)', 'type': 'new_api_error'}}[0m
2025-07-30 16:04:43 - [32mINFO[0m - Converted model name from aliyun/text-embedding-v4 to openai/text-embedding-v4 for LiteLLM compatibility[0m
2025-07-30 16:04:43 - [32mINFO[0m - NewAPI embedding request kwargs: {'model': 'openai/text-embedding-v4', 'api_base': 'http://***********:3000/v1', 'api_key': 'sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI'}[0m
2025-07-30 16:04:43 - [32mINFO[0m - Retrying request to /embeddings in 0.442499 seconds[0m
2025-07-30 16:04:44 - [32mINFO[0m - Retrying request to /embeddings in 0.795423 seconds[0m
2025-07-30 16:04:45 - [31mERROR[0m - Error getting embeddings: litellm.ServiceUnavailableError: ServiceUnavailableError: OpenAIException - Error code: 503 - {'error': {'message': '��ǰ���� �Ƽ��з����� �¶���ģ�� text-embedding-v4 �޿������� (request id: 20250730160443412500637FiryEUd4)', 'type': 'new_api_error'}}[0m
2025-07-30 16:04:45 - [33mWARNING[0m - Request failed (attempt 3): Error getting embeddings: litellm.ServiceUnavailableError: ServiceUnavailableError: OpenAIException - Error code: 503 - {'error': {'message': '��ǰ���� �Ƽ��з����� �¶���ģ�� text-embedding-v4 �޿������� (request id: 20250730160443412500637FiryEUd4)', 'type': 'new_api_error'}}[0m
2025-07-30 16:04:45 - [31mERROR[0m - Error running orchestrated ingestion: Error getting embeddings: litellm.ServiceUnavailableError: ServiceUnavailableError: OpenAIException - Error code: 503 - {'error': {'message': '��ǰ���� �Ƽ��з����� �¶���ģ�� text-embedding-v4 �޿������� (request id: 20250730160443412500637FiryEUd4)', 'type': 'new_api_error'}} 

Attempting to run without orchestration.[0m
2025-07-30 16:04:45 - [32mINFO[0m - Running ingestion without orchestration for file ��ְ�����ݽ���.pdf and document_id 55b09cdc-5de0-567a-bf36-dcdd237269fa.[0m
2025-07-30 16:04:46 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:04:46 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:04:46 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:05:01 - [32mINFO[0m - Converted model name from aliyun/text-embedding-v4 to openai/text-embedding-v4 for LiteLLM compatibility[0m
2025-07-30 16:05:01 - [32mINFO[0m - NewAPI embedding request kwargs: {'model': 'openai/text-embedding-v4', 'api_base': 'http://***********:3000/v1', 'api_key': 'sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI'}[0m
2025-07-30 16:05:01 - [32mINFO[0m - Retrying request to /embeddings in 0.413371 seconds[0m
2025-07-30 16:05:02 - [32mINFO[0m - Retrying request to /embeddings in 0.840141 seconds[0m
2025-07-30 16:05:03 - [31mERROR[0m - Error getting embeddings: litellm.ServiceUnavailableError: ServiceUnavailableError: OpenAIException - Error code: 503 - {'error': {'message': '��ǰ���� �Ƽ��з����� �¶���ģ�� text-embedding-v4 �޿������� (request id: 20250730160501268164184l2RQ7Vr7)', 'type': 'new_api_error'}}[0m
2025-07-30 16:05:03 - [33mWARNING[0m - Request failed (attempt 1): Error getting embeddings: litellm.ServiceUnavailableError: ServiceUnavailableError: OpenAIException - Error code: 503 - {'error': {'message': '��ǰ���� �Ƽ��з����� �¶���ģ�� text-embedding-v4 �޿������� (request id: 20250730160501268164184l2RQ7Vr7)', 'type': 'new_api_error'}}[0m
2025-07-30 16:05:03 - [32mINFO[0m - Converted model name from aliyun/text-embedding-v4 to openai/text-embedding-v4 for LiteLLM compatibility[0m
2025-07-30 16:05:03 - [32mINFO[0m - NewAPI embedding request kwargs: {'model': 'openai/text-embedding-v4', 'api_base': 'http://***********:3000/v1', 'api_key': 'sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI'}[0m
2025-07-30 16:05:04 - [32mINFO[0m - Retrying request to /embeddings in 0.491140 seconds[0m
2025-07-30 16:05:04 - [32mINFO[0m - Retrying request to /embeddings in 0.832452 seconds[0m
2025-07-30 16:05:05 - [31mERROR[0m - Error getting embeddings: litellm.ServiceUnavailableError: ServiceUnavailableError: OpenAIException - Error code: 503 - {'error': {'message': '��ǰ���� �Ƽ��з����� �¶���ģ�� text-embedding-v4 �޿������� (request id: 20250730160503730663040KFEynwtn)', 'type': 'new_api_error'}}[0m
2025-07-30 16:05:05 - [33mWARNING[0m - Request failed (attempt 2): Error getting embeddings: litellm.ServiceUnavailableError: ServiceUnavailableError: OpenAIException - Error code: 503 - {'error': {'message': '��ǰ���� �Ƽ��з����� �¶���ģ�� text-embedding-v4 �޿������� (request id: 20250730160503730663040KFEynwtn)', 'type': 'new_api_error'}}[0m
2025-07-30 16:05:06 - [32mINFO[0m - Converted model name from aliyun/text-embedding-v4 to openai/text-embedding-v4 for LiteLLM compatibility[0m
2025-07-30 16:05:06 - [32mINFO[0m - NewAPI embedding request kwargs: {'model': 'openai/text-embedding-v4', 'api_base': 'http://***********:3000/v1', 'api_key': 'sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI'}[0m
2025-07-30 16:05:06 - [32mINFO[0m - Retrying request to /embeddings in 0.451824 seconds[0m
2025-07-30 16:05:07 - [32mINFO[0m - Retrying request to /embeddings in 0.871641 seconds[0m
2025-07-30 16:05:08 - [31mERROR[0m - Error getting embeddings: litellm.ServiceUnavailableError: ServiceUnavailableError: OpenAIException - Error code: 503 - {'error': {'message': '��ǰ���� �Ƽ��з����� �¶���ģ�� text-embedding-v4 �޿������� (request id: 202507301605064967171207PVBt27E)', 'type': 'new_api_error'}}[0m
2025-07-30 16:05:08 - [33mWARNING[0m - Request failed (attempt 3): Error getting embeddings: litellm.ServiceUnavailableError: ServiceUnavailableError: OpenAIException - Error code: 503 - {'error': {'message': '��ǰ���� �Ƽ��з����� �¶���ģ�� text-embedding-v4 �޿������� (request id: 202507301605064967171207PVBt27E)', 'type': 'new_api_error'}}[0m
2025-07-30 16:05:08 - [33mWARNING[0m - 127.0.0.1:60338 - "POST /v3/documents HTTP/1.1" [33m400[0m
2025-07-30 16:07:37 - [32mINFO[0m - 127.0.0.1:60631 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 16:07:38 - [32mINFO[0m - 127.0.0.1:60631 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 16:12:39 - [32mINFO[0m - 127.0.0.1:61052 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 16:17:40 - [32mINFO[0m - 127.0.0.1:61452 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 16:17:40 - [32mINFO[0m - 127.0.0.1:61452 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 16:22:42 - [32mINFO[0m - 127.0.0.1:61879 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 16:27:43 - [32mINFO[0m - 127.0.0.1:62304 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 16:27:43 - [32mINFO[0m - 127.0.0.1:62304 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 16:32:12 - [32mINFO[0m - Shutting down[0m
2025-07-30 16:32:13 - [32mINFO[0m - Waiting for application shutdown.[0m
2025-07-30 16:32:13 - [32mINFO[0m - Application shutdown complete.[0m
2025-07-30 16:32:13 - [32mINFO[0m - Finished server process [16432][0m
2025-07-30 16:37:10 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 16:37:10 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://***********:3000/v1'} provider='newapi' base_model='aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 16:37:13 - [32mINFO[0m - Initialized NewAPIEmbeddingProvider with model: aliyun/text-embedding-v4[0m
2025-07-30 16:37:13 - [32mINFO[0m - API Base: http://***********:3000/v1[0m
2025-07-30 16:37:13 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://***********:3000/v1'} provider='newapi' base_model='aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-30 16:37:18 - [32mINFO[0m - Initialized NewAPIEmbeddingProvider with model: aliyun/text-embedding-v4[0m
2025-07-30 16:37:18 - [32mINFO[0m - API Base: http://***********:3000/v1[0m
2025-07-30 16:37:18 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 16:37:18 - [32mINFO[0m - Initializing R2RCompletionProvider...[0m
2025-07-30 16:37:18 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 16:37:41 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 16:37:47 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 16:37:47 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-30 16:37:47 - [32mINFO[0m - Initializing BcryptCryptoProvider[0m
2025-07-30 16:37:47 - [32mINFO[0m - Initializing DatabaseProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='postgres' user=None password=None host=None port=None db_name=None project_name=None postgres_configuration_settings=None default_collection_name='Default' default_collection_description='Your default collection.' collection_summary_system_prompt='system' collection_summary_prompt='collection_summary' disable_create_extension=False batch_size=1 graph_search_results_store_path=None graph_enrichment_settings=GraphEnrichmentSettings(force_graph_search_results_enrichment=False, graph_communities_prompt='graph_communities', max_summary_input_length=65536, generation_config=None, leiden_params={}) graph_creation_settings=GraphCreationSettings(graph_extraction_prompt='graph_extraction', graph_entity_description_prompt='graph_entity_description', entity_types=[], relation_types=[], chunk_merge_count=2, max_knowledge_relationships=100, max_description_input_length=65536, generation_config=None, automatic_deduplication=True) graph_search_settings=GraphSearchSettings(limits={}, enabled=True) limits=LimitSettings(global_per_min=60, route_per_min=20, monthly_limit=10000) maintenance=MaintenanceSettings(vacuum_schedule='0 3 * * *', vacuum_analyze=True, vacuum_full=False) route_limits={} user_limits={}.[0m
2025-07-30 16:37:48 - [32mINFO[0m - Connecting to Postgres via TCP/IP[0m
2025-07-30 16:37:48 - [32mINFO[0m - Initializing `PostgresDatabaseProvider`.[0m
2025-07-30 16:37:48 - [32mINFO[0m - Connecting with 230 connections to `asyncpg.create_pool`.[0m
2025-07-30 16:37:50 - [32mINFO[0m - Successfully connected to Postgres database and created connection pool.[0m
2025-07-30 16:37:50 - [32mINFO[0m - Creating table, if it does not exist: "cscsrag"."documents"[0m
2025-07-30 16:37:52 - [32mINFO[0m - Loading saved prompt: graph_extraction[0m
2025-07-30 16:37:52 - [32mINFO[0m - Loading saved prompt: rag_fusion[0m
2025-07-30 16:37:52 - [32mINFO[0m - Loading saved prompt: static_rag_agent[0m
2025-07-30 16:37:52 - [32mINFO[0m - Loading saved prompt: static_research_agent[0m
2025-07-30 16:37:52 - [32mINFO[0m - Loading saved prompt: summary[0m
2025-07-30 16:37:52 - [32mINFO[0m - Loading saved prompt: vision_pdf[0m
2025-07-30 16:37:52 - [32mINFO[0m - Loading saved prompt: hyde[0m
2025-07-30 16:37:52 - [32mINFO[0m - Loading saved prompt: rag[0m
2025-07-30 16:37:52 - [32mINFO[0m - Loading saved prompt: system[0m
2025-07-30 16:37:52 - [32mINFO[0m - Loading saved prompt: vision_img[0m
2025-07-30 16:37:52 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent[0m
2025-07-30 16:37:52 - [32mINFO[0m - Loading saved prompt: chunk_enrichment[0m
2025-07-30 16:37:52 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent_xml_tooling[0m
2025-07-30 16:37:52 - [32mINFO[0m - Loading saved prompt: graph_entity_description[0m
2025-07-30 16:37:52 - [32mINFO[0m - Loading saved prompt: collection_summary[0m
2025-07-30 16:37:52 - [32mINFO[0m - Loading saved prompt: graph_communities[0m
2025-07-30 16:37:52 - [32mINFO[0m - Loading prompts from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts[0m
2025-07-30 16:37:52 - [32mINFO[0m - Loading default prompt: chunk_enrichment from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\chunk_enrichment.yaml.[0m
2025-07-30 16:37:52 - [32mINFO[0m - Loading default prompt: collection_summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\collection_summary.yaml.[0m
2025-07-30 16:37:52 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\dynamic_rag_agent.yaml.[0m
2025-07-30 16:37:52 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent_xml_tooling from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\dynamic_rag_agent_xml_tooling.yaml.[0m
2025-07-30 16:37:52 - [32mINFO[0m - Loading default prompt: graph_communities from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_communities.yaml.[0m
2025-07-30 16:37:53 - [32mINFO[0m - Loading default prompt: graph_entity_description from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_entity_description.yaml.[0m
2025-07-30 16:37:53 - [32mINFO[0m - Loading default prompt: graph_extraction from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_extraction.yaml.[0m
2025-07-30 16:37:53 - [32mINFO[0m - Loading default prompt: hyde from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\hyde.yaml.[0m
2025-07-30 16:37:53 - [32mINFO[0m - Loading default prompt: rag from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\rag.yaml.[0m
2025-07-30 16:37:53 - [32mINFO[0m - Loading default prompt: rag_fusion from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\rag_fusion.yaml.[0m
2025-07-30 16:37:53 - [32mINFO[0m - Loading default prompt: static_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\static_rag_agent.yaml.[0m
2025-07-30 16:37:53 - [32mINFO[0m - Loading default prompt: static_research_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\static_research_agent.yaml.[0m
2025-07-30 16:37:54 - [32mINFO[0m - Loading default prompt: summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\summary.yaml.[0m
2025-07-30 16:37:54 - [32mINFO[0m - Loading default prompt: system from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\system.yaml.[0m
2025-07-30 16:37:54 - [32mINFO[0m - Loading default prompt: vision_img from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\vision_img.yaml.[0m
2025-07-30 16:37:54 - [32mINFO[0m - Loading default prompt: vision_pdf from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\vision_pdf.yaml.[0m
2025-07-30 16:37:58 - [32mINFO[0m - Using existing S3 bucket: rag[0m
2025-07-30 16:37:58 - [32mINFO[0m - Initializing OCRProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='mistral' model='mistral-ocr-latest' concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0[0m
2025-07-30 16:37:58 - [33mWARNING[0m - MISTRAL_API_KEY not set in environment, if you plan to use Mistral OCR, please set it.[0m
2025-07-30 16:38:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:38:01 - [32mINFO[0m - Initialized extra parser zerox for pdf[0m
2025-07-30 16:38:01 - [32mINFO[0m - Initialized extra parser ocr for pdf[0m
2025-07-30 16:38:01 - [32mINFO[0m - R2RIngestionProvider initialized with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' excluded_parsers=[] chunking_strategy=<ChunkingStrategy.RECURSIVE: 'recursive'> chunk_size=1024 chunk_overlap=512 chunk_enrichment_settings=ChunkEnrichmentSettings(enable_chunk_enrichment=False, n_chunks=2, generation_config=None, chunk_enrichment_prompt='chunk_enrichment') extra_parsers={'pdf': ['zerox', 'ocr']} audio_transcription_model=None vlm=None vlm_batch_size=20 vlm_max_tokens_to_sample=1024 max_concurrent_vlm_tasks=20 vlm_ocr_one_page_per_chunk=True skip_document_summary=False document_summary_system_prompt='system' document_summary_task_prompt='summary' chunks_for_document_summary=128 document_summary_model=None parser_overrides={} automatic_extraction=True document_summary_max_length=100000 separator=None[0m
2025-07-30 16:38:01 - [32mINFO[0m - Default admin user already exists.[0m
2025-07-30 16:38:01 - [32mINFO[0m - Initializing database maintenance service[0m
2025-07-30 16:38:01 - [32mINFO[0m - Scheduler started[0m
2025-07-30 16:38:01 - [32mINFO[0m - Scheduler started[0m
2025-07-30 16:38:01 - [32mINFO[0m - Adding job vacuum_database with trigger cron and kwargs {'minute': '0', 'hour': '3', 'day': '*', 'month': '*', 'day_of_week': '*', 'kwargs': {'full': False, 'analyze': True}}[0m
2025-07-30 16:38:02 - [32mINFO[0m - Added job "MaintenanceService.vacuum_database" to job store "default"[0m
2025-07-30 16:38:02 - [32mINFO[0m - Initializing ChunksRouter[0m
2025-07-30 16:38:02 - [32mINFO[0m - Initializing CollectionsRouter[0m
2025-07-30 16:38:02 - [32mINFO[0m - Initializing ConversationsRouter[0m
2025-07-30 16:38:02 - [32mINFO[0m - Initializing DocumentsRouter[0m
2025-07-30 16:38:02 - [32mINFO[0m - Initializing GraphRouter[0m
2025-07-30 16:38:02 - [32mINFO[0m - Initializing IndicesRouter[0m
2025-07-30 16:38:02 - [32mINFO[0m - Initializing PromptsRouter[0m
2025-07-30 16:38:02 - [32mINFO[0m - Initializing RetrievalRouter[0m
2025-07-30 16:38:02 - [32mINFO[0m - Initializing SystemRouter[0m
2025-07-30 16:38:02 - [32mINFO[0m - Initializing UsersRouter[0m
2025-07-30 16:38:03 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-30 16:38:04 - [32mINFO[0m - Started server process [27084][0m
2025-07-30 16:38:04 - [32mINFO[0m - Waiting for application startup.[0m
2025-07-30 16:38:04 - [32mINFO[0m - Application startup complete.[0m
2025-07-30 16:38:04 - [32mINFO[0m - Uvicorn running on http://0.0.0.0:7272 (Press CTRL+C to quit)[0m
2025-07-30 16:42:43 - [32mINFO[0m - 127.0.0.1:63971 - "POST /v3/users/login HTTP/1.1" [32m200[0m
2025-07-30 16:42:43 - [32mINFO[0m - 127.0.0.1:63971 - "OPTIONS /v3/users/me HTTP/1.1" [32m200[0m
2025-07-30 16:42:43 - [32mINFO[0m - 127.0.0.1:63971 - "GET /v3/users/me HTTP/1.1" [32m200[0m
2025-07-30 16:42:43 - [32mINFO[0m - 127.0.0.1:63971 - "OPTIONS /v3/system/settings HTTP/1.1" [32m200[0m
2025-07-30 16:42:44 - [32mINFO[0m - 127.0.0.1:63971 - "GET /v3/system/settings HTTP/1.1" [32m200[0m
2025-07-30 16:42:50 - [32mINFO[0m - 127.0.0.1:63973 - "OPTIONS /v3/system/status HTTP/1.1" [32m200[0m
2025-07-30 16:42:50 - [32mINFO[0m - 127.0.0.1:63973 - "OPTIONS /v3/system/status HTTP/1.1" [32m200[0m
2025-07-30 16:42:51 - [32mINFO[0m - 127.0.0.1:63973 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-07-30 16:42:51 - [32mINFO[0m - 127.0.0.1:63994 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-07-30 16:43:01 - [32mINFO[0m - 127.0.0.1:63995 - "OPTIONS /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-07-30 16:43:02 - [32mINFO[0m - 127.0.0.1:63995 - "GET /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-07-30 16:43:11 - [32mINFO[0m - 127.0.0.1:63996 - "OPTIONS /v3/documents/55b09cdc-5de0-567a-bf36-dcdd237269fa HTTP/1.1" [32m200[0m
2025-07-30 16:43:12 - [32mINFO[0m - 127.0.0.1:63996 - "DELETE /v3/documents/55b09cdc-5de0-567a-bf36-dcdd237269fa HTTP/1.1" [32m200[0m
2025-07-30 16:43:37 - [32mINFO[0m - 127.0.0.1:64073 - "OPTIONS /v3/documents HTTP/1.1" [32m200[0m
2025-07-30 16:43:37 - [33mWARNING[0m - Skipping data after last boundary[0m
2025-07-30 16:43:40 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:43:40 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:43:40 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:44:02 - [32mINFO[0m - NewAPI embedding request - model: aliyun/text-embedding-v4, texts count: 1[0m
2025-07-30 16:44:03 - [32mINFO[0m - NewAPI embedding request - model: aliyun/text-embedding-v4, texts count: 5[0m
2025-07-30 16:44:04 - [32mINFO[0m - Successful ingestion for document_id: 55b09cdc-5de0-567a-bf36-dcdd237269fa, with vector count: 5[0m
2025-07-30 16:44:06 - [33mWARNING[0m - Automatic extraction not yet implemented for `simple` ingestion workflows.[0m
2025-07-30 16:44:06 - [32mINFO[0m - 127.0.0.1:64073 - "POST /v3/documents HTTP/1.1" [32m202[0m
2025-07-30 16:45:19 - [32mINFO[0m - 127.0.0.1:64260 - "DELETE /v3/documents/55b09cdc-5de0-567a-bf36-dcdd237269fa HTTP/1.1" [32m200[0m
2025-07-30 16:45:34 - [32mINFO[0m - 127.0.0.1:64261 - "OPTIONS /v3/users/logout HTTP/1.1" [32m200[0m
2025-07-30 16:45:35 - [32mINFO[0m - 127.0.0.1:64261 - "POST /v3/users/logout HTTP/1.1" [32m200[0m
2025-07-30 16:46:00 - [33mWARNING[0m - Invalid password for user: <EMAIL>[0m
2025-07-30 16:46:00 - [33mWARNING[0m - 127.0.0.1:64348 - "POST /v3/users/login HTTP/1.1" [33m401[0m
2025-07-30 16:46:36 - [32mINFO[0m - 127.0.0.1:64350 - "POST /v3/users/login HTTP/1.1" [32m200[0m
2025-07-30 16:46:36 - [32mINFO[0m - 127.0.0.1:64350 - "GET /v3/users/me HTTP/1.1" [32m200[0m
2025-07-30 16:46:37 - [32mINFO[0m - 127.0.0.1:64350 - "GET /v3/system/settings HTTP/1.1" [32m200[0m
2025-07-30 16:46:38 - [32mINFO[0m - 127.0.0.1:64350 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-07-30 16:46:39 - [32mINFO[0m - 127.0.0.1:64431 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-07-30 16:47:07 - [33mWARNING[0m - Skipping data after last boundary[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:18 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:19 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:19 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:19 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:19 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:19 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:19 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:19 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:19 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:19 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:19 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:19 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-30 16:47:28 - [32mINFO[0m - NewAPI embedding request - model: aliyun/text-embedding-v4, texts count: 1[0m
2025-07-30 16:47:28 - [32mINFO[0m - NewAPI embedding request - model: aliyun/text-embedding-v4, texts count: 8[0m
2025-07-30 16:47:28 - [32mINFO[0m - NewAPI embedding request - model: aliyun/text-embedding-v4, texts count: 8[0m
2025-07-30 16:47:28 - [32mINFO[0m - NewAPI embedding request - model: aliyun/text-embedding-v4, texts count: 8[0m
2025-07-30 16:47:28 - [32mINFO[0m - NewAPI embedding request - model: aliyun/text-embedding-v4, texts count: 8[0m
2025-07-30 16:47:28 - [32mINFO[0m - NewAPI embedding request - model: aliyun/text-embedding-v4, texts count: 8[0m
2025-07-30 16:47:28 - [32mINFO[0m - NewAPI embedding request - model: aliyun/text-embedding-v4, texts count: 8[0m
2025-07-30 16:47:28 - [32mINFO[0m - NewAPI embedding request - model: aliyun/text-embedding-v4, texts count: 3[0m
2025-07-30 16:47:36 - [32mINFO[0m - Successful ingestion for document_id: 90bbe979-adc6-5bb9-8548-31309b4e8281, with vector count: 51[0m
2025-07-30 16:47:37 - [33mWARNING[0m - Automatic extraction not yet implemented for `simple` ingestion workflows.[0m
2025-07-30 16:47:37 - [32mINFO[0m - 127.0.0.1:64432 - "POST /v3/documents HTTP/1.1" [32m202[0m
2025-07-30 16:51:38 - [32mINFO[0m - 127.0.0.1:64932 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 16:51:39 - [32mINFO[0m - 127.0.0.1:64932 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 16:56:40 - [32mINFO[0m - 127.0.0.1:65349 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 17:01:41 - [32mINFO[0m - 127.0.0.1:49669 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 17:01:41 - [32mINFO[0m - 127.0.0.1:49669 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 17:06:42 - [32mINFO[0m - 127.0.0.1:50840 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 17:11:43 - [32mINFO[0m - 127.0.0.1:51382 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 17:11:43 - [32mINFO[0m - 127.0.0.1:51382 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 17:16:44 - [32mINFO[0m - 127.0.0.1:51861 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 17:21:45 - [32mINFO[0m - 127.0.0.1:52269 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 17:21:45 - [32mINFO[0m - 127.0.0.1:52269 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 17:26:46 - [32mINFO[0m - 127.0.0.1:52829 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 17:31:47 - [32mINFO[0m - 127.0.0.1:53234 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 17:31:47 - [32mINFO[0m - 127.0.0.1:53234 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 17:36:48 - [32mINFO[0m - 127.0.0.1:53644 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 17:41:49 - [32mINFO[0m - 127.0.0.1:54089 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 17:41:49 - [32mINFO[0m - 127.0.0.1:54089 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 17:46:50 - [32mINFO[0m - 127.0.0.1:54511 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 17:51:51 - [32mINFO[0m - 127.0.0.1:54869 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 17:51:51 - [32mINFO[0m - 127.0.0.1:54869 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 17:56:52 - [32mINFO[0m - 127.0.0.1:55278 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 18:01:53 - [32mINFO[0m - 127.0.0.1:55665 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 18:01:53 - [32mINFO[0m - 127.0.0.1:55665 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 18:06:54 - [32mINFO[0m - 127.0.0.1:56023 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 18:11:55 - [32mINFO[0m - 127.0.0.1:56424 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 18:11:55 - [32mINFO[0m - 127.0.0.1:56424 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 18:16:56 - [32mINFO[0m - 127.0.0.1:56794 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 18:21:57 - [32mINFO[0m - 127.0.0.1:57155 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 18:21:57 - [32mINFO[0m - 127.0.0.1:57155 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 18:26:58 - [32mINFO[0m - 127.0.0.1:57532 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 18:31:59 - [32mINFO[0m - 127.0.0.1:57920 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 18:31:59 - [32mINFO[0m - 127.0.0.1:57920 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 18:37:00 - [32mINFO[0m - 127.0.0.1:58304 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 18:42:01 - [32mINFO[0m - 127.0.0.1:58753 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 18:42:01 - [32mINFO[0m - 127.0.0.1:58753 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 18:47:02 - [32mINFO[0m - 127.0.0.1:59166 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 18:52:03 - [32mINFO[0m - 127.0.0.1:59554 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 18:52:03 - [32mINFO[0m - 127.0.0.1:59554 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 18:57:04 - [32mINFO[0m - 127.0.0.1:59937 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 19:02:05 - [32mINFO[0m - 127.0.0.1:60327 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 19:02:05 - [32mINFO[0m - 127.0.0.1:60327 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 19:07:06 - [32mINFO[0m - 127.0.0.1:60693 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 19:12:07 - [32mINFO[0m - 127.0.0.1:61086 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 19:12:07 - [32mINFO[0m - 127.0.0.1:61086 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 19:17:08 - [32mINFO[0m - 127.0.0.1:61468 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 19:22:09 - [32mINFO[0m - 127.0.0.1:61848 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 19:22:09 - [32mINFO[0m - 127.0.0.1:61848 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 19:27:10 - [32mINFO[0m - 127.0.0.1:62288 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 19:32:11 - [32mINFO[0m - 127.0.0.1:62678 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 19:32:11 - [32mINFO[0m - 127.0.0.1:62678 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 19:37:12 - [32mINFO[0m - 127.0.0.1:63066 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 19:42:13 - [32mINFO[0m - 127.0.0.1:63490 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 19:42:13 - [32mINFO[0m - 127.0.0.1:63490 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 19:47:14 - [32mINFO[0m - 127.0.0.1:63884 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 19:52:15 - [32mINFO[0m - 127.0.0.1:64244 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 19:52:15 - [32mINFO[0m - 127.0.0.1:64244 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 19:57:16 - [32mINFO[0m - 127.0.0.1:64697 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 20:02:17 - [32mINFO[0m - 127.0.0.1:65094 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 20:02:17 - [32mINFO[0m - 127.0.0.1:65094 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 20:07:18 - [32mINFO[0m - 127.0.0.1:65489 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 20:12:19 - [32mINFO[0m - 127.0.0.1:50317 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 20:12:19 - [32mINFO[0m - 127.0.0.1:50317 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 20:17:20 - [32mINFO[0m - 127.0.0.1:50922 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 20:22:21 - [32mINFO[0m - 127.0.0.1:51426 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 20:22:21 - [32mINFO[0m - 127.0.0.1:51426 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 20:27:22 - [32mINFO[0m - 127.0.0.1:51860 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 20:32:23 - [32mINFO[0m - 127.0.0.1:52275 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 20:32:23 - [32mINFO[0m - 127.0.0.1:52275 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 20:37:24 - [32mINFO[0m - 127.0.0.1:52822 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 20:42:25 - [32mINFO[0m - 127.0.0.1:53309 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 20:42:25 - [32mINFO[0m - 127.0.0.1:53309 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 20:47:26 - [32mINFO[0m - 127.0.0.1:53749 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 20:52:27 - [32mINFO[0m - 127.0.0.1:54120 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 20:52:27 - [32mINFO[0m - 127.0.0.1:54120 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 20:57:28 - [32mINFO[0m - 127.0.0.1:54525 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 21:02:29 - [32mINFO[0m - 127.0.0.1:54919 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 21:02:29 - [32mINFO[0m - 127.0.0.1:54919 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 21:07:30 - [32mINFO[0m - 127.0.0.1:55345 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 21:12:31 - [32mINFO[0m - 127.0.0.1:55728 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 21:12:31 - [32mINFO[0m - 127.0.0.1:55728 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 21:17:32 - [32mINFO[0m - 127.0.0.1:56131 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 21:22:33 - [32mINFO[0m - 127.0.0.1:56518 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 21:22:33 - [32mINFO[0m - 127.0.0.1:56518 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 21:27:34 - [32mINFO[0m - 127.0.0.1:56900 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 21:32:35 - [32mINFO[0m - 127.0.0.1:57278 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 21:32:35 - [32mINFO[0m - 127.0.0.1:57278 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 21:37:36 - [32mINFO[0m - 127.0.0.1:57696 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 21:42:37 - [32mINFO[0m - 127.0.0.1:58125 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 21:42:37 - [32mINFO[0m - 127.0.0.1:58125 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 21:47:38 - [32mINFO[0m - 127.0.0.1:58545 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 21:52:39 - [32mINFO[0m - 127.0.0.1:58934 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 21:52:39 - [32mINFO[0m - 127.0.0.1:58934 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 21:57:40 - [32mINFO[0m - 127.0.0.1:59329 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 22:02:41 - [32mINFO[0m - 127.0.0.1:59734 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 22:02:41 - [32mINFO[0m - 127.0.0.1:59734 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 22:07:42 - [32mINFO[0m - 127.0.0.1:60072 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 22:12:43 - [32mINFO[0m - 127.0.0.1:60435 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 22:12:43 - [32mINFO[0m - 127.0.0.1:60435 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 22:17:44 - [32mINFO[0m - 127.0.0.1:60794 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 22:22:45 - [32mINFO[0m - 127.0.0.1:61141 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 22:22:45 - [32mINFO[0m - 127.0.0.1:61141 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 22:27:46 - [32mINFO[0m - 127.0.0.1:61483 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 22:32:47 - [32mINFO[0m - 127.0.0.1:61856 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 22:32:47 - [32mINFO[0m - 127.0.0.1:61856 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 22:37:48 - [32mINFO[0m - 127.0.0.1:62253 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 22:42:49 - [32mINFO[0m - 127.0.0.1:62663 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 22:42:49 - [32mINFO[0m - 127.0.0.1:62663 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 22:47:50 - [32mINFO[0m - 127.0.0.1:63079 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 22:52:51 - [32mINFO[0m - 127.0.0.1:63422 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 22:52:51 - [32mINFO[0m - 127.0.0.1:63422 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 22:57:52 - [32mINFO[0m - 127.0.0.1:63816 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 23:02:53 - [32mINFO[0m - 127.0.0.1:64154 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 23:02:53 - [32mINFO[0m - 127.0.0.1:64154 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 23:07:54 - [32mINFO[0m - 127.0.0.1:64501 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 23:12:55 - [32mINFO[0m - 127.0.0.1:64946 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 23:12:55 - [32mINFO[0m - 127.0.0.1:64946 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 23:17:56 - [32mINFO[0m - 127.0.0.1:65306 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 23:22:57 - [32mINFO[0m - 127.0.0.1:49254 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 23:22:57 - [32mINFO[0m - 127.0.0.1:49254 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 23:27:58 - [32mINFO[0m - 127.0.0.1:50667 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 23:32:59 - [32mINFO[0m - 127.0.0.1:51032 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 23:32:59 - [32mINFO[0m - 127.0.0.1:51032 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 23:38:00 - [32mINFO[0m - 127.0.0.1:51562 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 23:43:01 - [32mINFO[0m - 127.0.0.1:52038 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 23:43:01 - [32mINFO[0m - 127.0.0.1:52038 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 23:48:02 - [32mINFO[0m - 127.0.0.1:52422 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 23:53:03 - [32mINFO[0m - 127.0.0.1:52886 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 23:53:03 - [32mINFO[0m - 127.0.0.1:52886 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-30 23:58:04 - [32mINFO[0m - 127.0.0.1:53267 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 00:03:05 - [32mINFO[0m - 127.0.0.1:53676 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 00:03:05 - [32mINFO[0m - 127.0.0.1:53676 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 00:08:06 - [32mINFO[0m - 127.0.0.1:54046 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 00:13:07 - [32mINFO[0m - 127.0.0.1:54398 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 00:13:07 - [32mINFO[0m - 127.0.0.1:54398 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 00:18:08 - [32mINFO[0m - 127.0.0.1:54771 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 00:23:09 - [32mINFO[0m - 127.0.0.1:55120 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 00:23:09 - [32mINFO[0m - 127.0.0.1:55120 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 00:28:10 - [32mINFO[0m - 127.0.0.1:55477 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 00:33:11 - [32mINFO[0m - 127.0.0.1:55852 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 00:33:11 - [32mINFO[0m - 127.0.0.1:55852 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 00:38:12 - [32mINFO[0m - 127.0.0.1:56266 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 00:43:13 - [32mINFO[0m - 127.0.0.1:56689 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 00:43:13 - [32mINFO[0m - 127.0.0.1:56689 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 00:48:14 - [32mINFO[0m - 127.0.0.1:57098 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 00:53:15 - [32mINFO[0m - 127.0.0.1:57479 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 00:53:15 - [32mINFO[0m - 127.0.0.1:57479 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 00:58:16 - [32mINFO[0m - 127.0.0.1:57844 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 01:03:17 - [32mINFO[0m - 127.0.0.1:58243 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 01:03:17 - [32mINFO[0m - 127.0.0.1:58243 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 01:08:18 - [32mINFO[0m - 127.0.0.1:58657 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 01:13:19 - [32mINFO[0m - 127.0.0.1:59041 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 01:13:19 - [32mINFO[0m - 127.0.0.1:59041 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 01:18:20 - [32mINFO[0m - 127.0.0.1:59420 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 01:23:21 - [32mINFO[0m - 127.0.0.1:59784 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 01:23:21 - [32mINFO[0m - 127.0.0.1:59784 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 01:28:22 - [32mINFO[0m - 127.0.0.1:60152 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 01:33:23 - [32mINFO[0m - 127.0.0.1:60529 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 01:33:23 - [32mINFO[0m - 127.0.0.1:60529 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 01:38:24 - [32mINFO[0m - 127.0.0.1:60916 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 01:43:25 - [32mINFO[0m - 127.0.0.1:61280 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 01:43:25 - [32mINFO[0m - 127.0.0.1:61280 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 01:48:26 - [32mINFO[0m - 127.0.0.1:61654 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 01:53:27 - [32mINFO[0m - 127.0.0.1:62037 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 01:53:27 - [32mINFO[0m - 127.0.0.1:62037 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 01:58:28 - [32mINFO[0m - 127.0.0.1:62503 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 02:03:29 - [32mINFO[0m - 127.0.0.1:62878 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 02:03:29 - [32mINFO[0m - 127.0.0.1:62878 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 02:08:30 - [32mINFO[0m - 127.0.0.1:63213 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 02:13:31 - [32mINFO[0m - 127.0.0.1:63579 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 02:13:31 - [32mINFO[0m - 127.0.0.1:63579 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 02:18:32 - [32mINFO[0m - 127.0.0.1:63936 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 02:23:33 - [32mINFO[0m - 127.0.0.1:64279 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 02:23:33 - [32mINFO[0m - 127.0.0.1:64279 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 02:28:34 - [32mINFO[0m - 127.0.0.1:64696 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 02:33:35 - [32mINFO[0m - 127.0.0.1:65066 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 02:33:35 - [32mINFO[0m - 127.0.0.1:65066 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 02:38:36 - [32mINFO[0m - 127.0.0.1:65512 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 02:43:37 - [32mINFO[0m - 127.0.0.1:50369 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 02:43:37 - [32mINFO[0m - 127.0.0.1:50369 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 02:48:38 - [32mINFO[0m - 127.0.0.1:50953 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 02:53:39 - [32mINFO[0m - 127.0.0.1:51398 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 02:53:39 - [32mINFO[0m - 127.0.0.1:51398 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 02:58:40 - [32mINFO[0m - 127.0.0.1:51857 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 03:00:00 - [32mINFO[0m - Running job "MaintenanceService.vacuum_database (trigger: cron[month='*', day='*', day_of_week='*', hour='3', minute='0'], next run at: 2025-08-01 03:00:00 CST)" (scheduled at 2025-07-31 03:00:00+08:00)[0m
2025-07-31 03:00:00 - [32mINFO[0m - Database vacuum completed successfully in 0.93 seconds[0m
2025-07-31 03:00:00 - [32mINFO[0m - Job "MaintenanceService.vacuum_database (trigger: cron[month='*', day='*', day_of_week='*', hour='3', minute='0'], next run at: 2025-08-01 03:00:00 CST)" executed successfully[0m
2025-07-31 03:03:41 - [32mINFO[0m - 127.0.0.1:52226 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 03:03:41 - [32mINFO[0m - 127.0.0.1:52226 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 03:08:42 - [32mINFO[0m - 127.0.0.1:52586 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 03:13:43 - [32mINFO[0m - 127.0.0.1:53047 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 03:13:43 - [32mINFO[0m - 127.0.0.1:53047 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 03:18:44 - [32mINFO[0m - 127.0.0.1:53409 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 03:23:45 - [32mINFO[0m - 127.0.0.1:53766 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 03:23:45 - [32mINFO[0m - 127.0.0.1:53766 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 03:28:46 - [32mINFO[0m - 127.0.0.1:54118 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 03:33:47 - [32mINFO[0m - 127.0.0.1:54474 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 03:33:47 - [32mINFO[0m - 127.0.0.1:54474 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 03:38:48 - [32mINFO[0m - 127.0.0.1:54878 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 03:43:49 - [32mINFO[0m - 127.0.0.1:55242 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 03:43:49 - [32mINFO[0m - 127.0.0.1:55242 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 03:48:50 - [32mINFO[0m - 127.0.0.1:55601 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 03:53:51 - [32mINFO[0m - 127.0.0.1:55951 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 03:53:51 - [32mINFO[0m - 127.0.0.1:55951 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 03:58:52 - [32mINFO[0m - 127.0.0.1:56351 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 04:03:53 - [32mINFO[0m - 127.0.0.1:56716 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 04:03:53 - [32mINFO[0m - 127.0.0.1:56716 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 04:08:54 - [32mINFO[0m - 127.0.0.1:57062 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 04:13:55 - [32mINFO[0m - 127.0.0.1:57408 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 04:13:55 - [32mINFO[0m - 127.0.0.1:57408 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 04:18:56 - [32mINFO[0m - 127.0.0.1:57754 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 04:23:57 - [32mINFO[0m - 127.0.0.1:58078 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 04:23:57 - [32mINFO[0m - 127.0.0.1:58078 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 04:28:58 - [32mINFO[0m - 127.0.0.1:58433 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 04:33:59 - [32mINFO[0m - 127.0.0.1:58782 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 04:33:59 - [32mINFO[0m - 127.0.0.1:58782 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 04:39:00 - [32mINFO[0m - 127.0.0.1:59195 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 04:44:01 - [32mINFO[0m - 127.0.0.1:59592 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 04:44:01 - [32mINFO[0m - 127.0.0.1:59592 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 04:49:02 - [32mINFO[0m - 127.0.0.1:59959 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 04:54:03 - [32mINFO[0m - 127.0.0.1:60284 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 04:54:03 - [32mINFO[0m - 127.0.0.1:60284 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 04:59:04 - [32mINFO[0m - 127.0.0.1:60679 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 05:04:05 - [32mINFO[0m - 127.0.0.1:61005 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 05:04:05 - [32mINFO[0m - 127.0.0.1:61005 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 05:09:06 - [32mINFO[0m - 127.0.0.1:61367 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 05:14:07 - [32mINFO[0m - 127.0.0.1:61727 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 05:14:07 - [32mINFO[0m - 127.0.0.1:61727 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 05:19:08 - [32mINFO[0m - 127.0.0.1:62153 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 05:24:09 - [32mINFO[0m - 127.0.0.1:62496 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 05:24:09 - [32mINFO[0m - 127.0.0.1:62496 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 05:29:10 - [32mINFO[0m - 127.0.0.1:62863 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 05:34:11 - [32mINFO[0m - 127.0.0.1:63207 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 05:34:11 - [32mINFO[0m - 127.0.0.1:63207 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 05:39:12 - [32mINFO[0m - 127.0.0.1:63604 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 05:44:13 - [32mINFO[0m - 127.0.0.1:63994 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 05:44:13 - [32mINFO[0m - 127.0.0.1:63994 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 05:49:14 - [32mINFO[0m - 127.0.0.1:64379 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 05:54:15 - [32mINFO[0m - 127.0.0.1:64829 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 05:54:15 - [32mINFO[0m - 127.0.0.1:64829 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 05:59:16 - [32mINFO[0m - 127.0.0.1:65217 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 06:04:17 - [32mINFO[0m - 127.0.0.1:49192 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 06:04:17 - [32mINFO[0m - 127.0.0.1:49192 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 06:09:18 - [32mINFO[0m - 127.0.0.1:50486 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 06:14:19 - [32mINFO[0m - 127.0.0.1:50950 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 06:14:19 - [32mINFO[0m - 127.0.0.1:50950 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 06:19:20 - [32mINFO[0m - 127.0.0.1:51431 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 06:24:21 - [32mINFO[0m - 127.0.0.1:51792 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 06:24:21 - [32mINFO[0m - 127.0.0.1:51792 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 06:29:22 - [32mINFO[0m - 127.0.0.1:52195 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 06:34:23 - [32mINFO[0m - 127.0.0.1:52556 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 06:34:23 - [32mINFO[0m - 127.0.0.1:52556 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 06:39:24 - [32mINFO[0m - 127.0.0.1:53060 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 06:44:25 - [32mINFO[0m - 127.0.0.1:53508 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 06:44:25 - [32mINFO[0m - 127.0.0.1:53508 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 06:49:26 - [32mINFO[0m - 127.0.0.1:53902 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 06:54:27 - [32mINFO[0m - 127.0.0.1:54245 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 06:54:27 - [32mINFO[0m - 127.0.0.1:54245 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 06:59:28 - [32mINFO[0m - 127.0.0.1:54635 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 07:04:29 - [32mINFO[0m - 127.0.0.1:54972 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 07:04:29 - [32mINFO[0m - 127.0.0.1:54972 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 07:09:30 - [32mINFO[0m - 127.0.0.1:55342 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 07:14:31 - [32mINFO[0m - 127.0.0.1:55681 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 07:14:31 - [32mINFO[0m - 127.0.0.1:55681 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 07:19:32 - [32mINFO[0m - 127.0.0.1:56031 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 07:24:33 - [32mINFO[0m - 127.0.0.1:56372 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 07:24:33 - [32mINFO[0m - 127.0.0.1:56372 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 07:29:34 - [32mINFO[0m - 127.0.0.1:56709 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 07:34:35 - [32mINFO[0m - 127.0.0.1:57098 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 07:34:35 - [32mINFO[0m - 127.0.0.1:57098 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 07:39:36 - [32mINFO[0m - 127.0.0.1:57484 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 07:44:37 - [32mINFO[0m - 127.0.0.1:57854 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 07:44:37 - [32mINFO[0m - 127.0.0.1:57854 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 07:49:38 - [32mINFO[0m - 127.0.0.1:58223 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 07:54:39 - [32mINFO[0m - 127.0.0.1:58559 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 07:54:39 - [32mINFO[0m - 127.0.0.1:58559 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 07:59:40 - [32mINFO[0m - 127.0.0.1:58938 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 08:04:41 - [32mINFO[0m - 127.0.0.1:59307 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 08:04:41 - [32mINFO[0m - 127.0.0.1:59307 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 08:09:42 - [32mINFO[0m - 127.0.0.1:59684 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 08:14:43 - [32mINFO[0m - 127.0.0.1:60053 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 08:14:43 - [32mINFO[0m - 127.0.0.1:60053 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 08:19:44 - [32mINFO[0m - 127.0.0.1:60417 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 08:24:45 - [32mINFO[0m - 127.0.0.1:60759 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 08:24:45 - [32mINFO[0m - 127.0.0.1:60759 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 08:29:46 - [32mINFO[0m - 127.0.0.1:61130 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 08:34:47 - [32mINFO[0m - 127.0.0.1:61481 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 08:34:47 - [32mINFO[0m - 127.0.0.1:61481 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 08:39:48 - [32mINFO[0m - 127.0.0.1:62048 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 08:40:57 - [32mINFO[0m - 127.0.0.1:62259 - "OPTIONS /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-07-31 08:40:58 - [32mINFO[0m - 127.0.0.1:62259 - "GET /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-07-31 08:45:58 - [32mINFO[0m - 127.0.0.1:62766 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 08:45:58 - [32mINFO[0m - 127.0.0.1:62766 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 08:51:00 - [32mINFO[0m - 127.0.0.1:63467 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 08:56:01 - [32mINFO[0m - 127.0.0.1:64204 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 08:56:01 - [32mINFO[0m - 127.0.0.1:64204 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 08:59:09 - [32mINFO[0m - Shutting down[0m
2025-07-31 08:59:09 - [32mINFO[0m - Waiting for application shutdown.[0m
2025-07-31 08:59:09 - [32mINFO[0m - Application shutdown complete.[0m
2025-07-31 08:59:09 - [32mINFO[0m - Finished server process [27084][0m
2025-07-31 17:24:15 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-31 17:24:15 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://***********:3000/v1'} provider='newapi' base_model='aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-31 17:24:20 - [32mINFO[0m - Initialized NewAPIEmbeddingProvider with model: aliyun/text-embedding-v4[0m
2025-07-31 17:24:20 - [32mINFO[0m - API Base: http://***********:3000/v1[0m
2025-07-31 17:24:20 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://***********:3000/v1'} provider='newapi' base_model='aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-31 17:24:24 - [32mINFO[0m - Initialized NewAPIEmbeddingProvider with model: aliyun/text-embedding-v4[0m
2025-07-31 17:24:24 - [32mINFO[0m - API Base: http://***********:3000/v1[0m
2025-07-31 17:24:24 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-31 17:24:24 - [32mINFO[0m - Initializing R2RCompletionProvider...[0m
2025-07-31 17:24:24 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-31 17:24:37 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-31 17:24:41 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-31 17:24:41 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-31 17:24:41 - [32mINFO[0m - Initializing BcryptCryptoProvider[0m
2025-07-31 17:24:41 - [32mINFO[0m - Initializing DatabaseProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='postgres' user=None password=None host=None port=None db_name=None project_name=None postgres_configuration_settings=None default_collection_name='Default' default_collection_description='Your default collection.' collection_summary_system_prompt='system' collection_summary_prompt='collection_summary' disable_create_extension=False batch_size=1 graph_search_results_store_path=None graph_enrichment_settings=GraphEnrichmentSettings(force_graph_search_results_enrichment=False, graph_communities_prompt='graph_communities', max_summary_input_length=65536, generation_config=None, leiden_params={}) graph_creation_settings=GraphCreationSettings(graph_extraction_prompt='graph_extraction', graph_entity_description_prompt='graph_entity_description', entity_types=[], relation_types=[], chunk_merge_count=2, max_knowledge_relationships=100, max_description_input_length=65536, generation_config=None, automatic_deduplication=True) graph_search_settings=GraphSearchSettings(limits={}, enabled=True) limits=LimitSettings(global_per_min=60, route_per_min=20, monthly_limit=10000) maintenance=MaintenanceSettings(vacuum_schedule='0 3 * * *', vacuum_analyze=True, vacuum_full=False) route_limits={} user_limits={}.[0m
2025-07-31 17:24:41 - [32mINFO[0m - Connecting to Postgres via TCP/IP[0m
2025-07-31 17:24:41 - [32mINFO[0m - Initializing `PostgresDatabaseProvider`.[0m
2025-07-31 17:24:41 - [32mINFO[0m - Connecting with 230 connections to `asyncpg.create_pool`.[0m
